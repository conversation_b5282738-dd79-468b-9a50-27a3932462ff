// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CompleteWizard() {
  return ObfuscatedCall(0, []);
}

export function DetectGame() {
  return ObfuscatedCall(1, []);
}

export function GetAppState() {
  return ObfuscatedCall(2, []);
}

export function GetAvailableKeys() {
  return ObfuscatedCall(3, []);
}

export function GetConfig() {
  return ObfuscatedCall(4, []);
}

export function GetWizardState() {
  return ObfuscatedCall(5, []);
}

export function InstallDriver() {
  return ObfuscatedCall(6, []);
}

export function LoadConfig() {
  return ObfuscatedCall(7, []);
}

export function NextWizardStep() {
  return ObfuscatedCall(8, []);
}

export function PreviousWizardStep() {
  return ObfuscatedCall(9, []);
}

export function RemoveCabalAlwaysOnTop() {
  return ObfuscatedCall(10, []);
}

export function SaveConfig() {
  return ObfuscatedCall(11, []);
}

export function SetCabalAlwaysOnTop() {
  return ObfuscatedCall(12, []);
}

export function SetLockKey(arg1) {
  return ObfuscatedCall(13, [arg1]);
}

export function SetSpaceSpamInterval(arg1) {
  return ObfuscatedCall(14, [arg1]);
}

export function SetTimerInterval(arg1) {
  return ObfuscatedCall(15, [arg1]);
}

export function StartBot() {
  return ObfuscatedCall(16, []);
}

export function StartKeyRecording() {
  return ObfuscatedCall(17, []);
}

export function StopBot() {
  return ObfuscatedCall(18, []);
}

export function StopKeyRecording() {
  return ObfuscatedCall(19, []);
}

export function ToggleOverlay(arg1) {
  return ObfuscatedCall(20, [arg1]);
}

export function ToggleSpaceSpam(arg1) {
  return ObfuscatedCall(21, [arg1]);
}

export function UpdateAction(arg1, arg2, arg3, arg4) {
  return ObfuscatedCall(22, [arg1, arg2, arg3, arg4]);
}

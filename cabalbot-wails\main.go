package main

import (
	"embed"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create application with options matching HTML design
	err := wails.Run(&options.App{
		Title:  "CABALBOT v2.0 - Trình Hỗ Trợ Tự Động",
		Width:  600, // Match HTML design
		Height: 400, // Match HTML design
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 15, G: 20, B: 30, A: 1}, // Match dark theme
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
		// Window options for overlay support
		WindowStartState: options.Normal,
		Frameless:        false, // Can be toggled for overlay mode
		AlwaysOnTop:      false, // Can be toggled for overlay mode
	})

	if err != nil {
		println("Error:", err.Error())
	}
}

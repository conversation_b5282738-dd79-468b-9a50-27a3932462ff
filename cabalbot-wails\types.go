package main

import (
	"os"
	"sync"
	"syscall"
)

// Constants for key scan codes (DirectInput scan codes)
const (
	SCANCODE_ESC   = 0x01
	SCANCODE_1     = 0x02
	SCANCODE_2     = 0x03
	SCANCODE_3     = 0x04
	SCANCODE_4     = 0x05
	SCANCODE_5     = 0x06
	SCANCODE_6     = 0x07
	SCANCODE_7     = 0x08
	SCANCODE_8     = 0x09
	SCANCODE_9     = 0x0A
	SCANCODE_0     = 0x0B
	SCANCODE_MINUS = 0x0C
	SCANCODE_PLUS  = 0x0D
	SCANCODE_TAB   = 0x0F
	SCANCODE_Q     = 0x10
	SCANCODE_W     = 0x11
	SCANCODE_E     = 0x12
	SCANCODE_R     = 0x13
	SCANCODE_T     = 0x14
	SCANCODE_Y     = 0x15
	SCANCODE_U     = 0x16
	SCANCODE_I     = 0x17
	SCANCODE_O     = 0x18
	SCANCODE_P     = 0x19
	SCANCODE_A     = 0x1E
	SCANCODE_S     = 0x1F
	SCANCODE_D     = 0x20
	SCANCODE_F     = 0x21
	SCANCODE_G     = 0x22
	SCANCODE_H     = 0x23
	SCANCODE_J     = 0x24
	SCANCODE_K     = 0x25
	SCANCODE_L     = 0x26
	SCANCODE_Z     = 0x2C
	SCANCODE_X     = 0x2D
	SCANCODE_C     = 0x2E
	SCANCODE_V     = 0x2F
	SCANCODE_B     = 0x30
	SCANCODE_N     = 0x31
	SCANCODE_M     = 0x32
	SCANCODE_SPACE = 0x39
	SCANCODE_ALT   = 0x38
	SCANCODE_CTRL  = 0x1D
	SCANCODE_SHIFT = 0x2A
	SCANCODE_F1    = 0x3B
	SCANCODE_F2    = 0x3C
	SCANCODE_F3    = 0x3D
	SCANCODE_F4    = 0x3E
	SCANCODE_F5    = 0x3F
	SCANCODE_F6    = 0x40
	SCANCODE_F7    = 0x41
	SCANCODE_F8    = 0x42
	SCANCODE_F9    = 0x43
	SCANCODE_F10   = 0x44
	SCANCODE_F11   = 0x57
	SCANCODE_F12   = 0x58
)

// Interception driver constants
const (
	INTERCEPTION_KEYBOARD   = 0
	INTERCEPTION_MOUSE      = 1
	INTERCEPTION_MAX_DEVICE = 20
)

// InterceptionKeyStroke represents a keyboard event in the Interception driver
type InterceptionKeyStroke struct {
	Code        uint16
	State       uint16
	Information uint32
}

// InterceptionDLL represents the loaded Interception DLL
type InterceptionDLL struct {
	dll              *syscall.DLL
	createContext    *syscall.Proc
	destroyContext   *syscall.Proc
	send             *syscall.Proc
	context          uintptr
	keyboardDeviceID int
	initialized      bool
}

// BotAction represents an action in the bot sequence
type BotAction struct {
	Key      string  `json:"key"`
	ScanCode uint16  `json:"scanCode"`
	Duration float64 `json:"duration"`
	Enabled  bool    `json:"enabled"`
}

// BotConfig represents the bot configuration
type BotConfig struct {
	Actions           []BotAction `json:"actions"`
	TimerInterval     float64     `json:"timerInterval"`
	LockKey           string      `json:"lockKey"`
	LockScanCode      uint16      `json:"lockScanCode"`
	Running           bool        `json:"running"`
	SpaceSpamEnabled  bool        `json:"spaceSpamEnabled"`
	SpaceSpamInterval int         `json:"spaceSpamInterval"`
	StopChan          chan struct{}
	SpaceStopChan     chan struct{}
	Mutex             sync.Mutex
}

// WizardState represents the state of the setup wizard
type WizardState struct {
	CurrentStep int
	TotalSteps  int
	Completed   bool
}

// GameProcessInfo contains info about the detected game process
type GameProcessInfo struct {
	ProcessID uint32
	Path      string
	Found     bool
}

// KeyRecordingState tracks state for direct key input recording
type KeyRecordingState struct {
	Recording      bool
	TargetKey      *string
	TargetScanCode *uint16
}

// AppState represents the application state
type AppState struct {
	config             *BotConfig
	statusMessage      string
	selectedTab        int
	editingAction      int
	tempKey            string
	tempDuration       string
	installStatus      string
	driverStatus       string
	overlayEnabled     bool
	transparency       float32
	overlayPosition    string
	directInputEnabled bool
	wizardState        WizardState
	gameProcess        GameProcessInfo
	keyRecordingState  KeyRecordingState
	logFile            *os.File
}

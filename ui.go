package main

import (
	"fmt"
	"image/color"
	"strconv"
	"time"

	g "github.com/AllenDang/giu"
)

// Color scheme matching the target design
var (
	colorBackground    = color.RGBA{15, 20, 30, 255}    // Dark blue background
	colorPrimary       = color.RGBA{255, 165, 0, 255}   // Orange accent (CABALBOT)
	colorSecondary     = color.RGBA{70, 130, 180, 255}  // Steel blue
	colorSuccess       = color.RGBA{0, 255, 127, 255}   // Spring green
	colorWarning       = color.RGBA{255, 215, 0, 255}   // Gold
	colorDanger        = color.RGBA{220, 20, 60, 255}   // Crimson
	colorText          = color.RGBA{255, 255, 255, 255} // White
	colorTextSecondary = color.RGBA{180, 180, 180, 255} // Light gray
	colorCardBg        = color.RGBA{25, 35, 50, 200}    // Semi-transparent card
	colorButtonBg      = color.RGBA{40, 50, 70, 255}    // Button background
	colorInputBg       = color.RGBA{30, 40, 55, 255}    // Input background
	colorTabActive     = color.RGBA{255, 165, 0, 100}   // Orange tab active
	colorTabInactive   = color.RGBA{60, 70, 90, 150}    // Gray tab inactive
)

// getCurrentDateTime returns formatted current time
func getCurrentDateTime() string {
	return time.Now().Format("15:04 PM")
}

// renderMainLayout renders the complete application layout matching target design
func renderMainLayout() g.Layout {
	return g.Layout{
		// Apply dark theme styling
		g.Custom(func() {
			// Window background
			g.PushStyleColor(g.StyleColorWindowBg, colorBackground)
			g.PushStyleColor(g.StyleColorChildBg, colorCardBg)

			// Text colors
			g.PushStyleColor(g.StyleColorText, colorText)

			// Button styling
			g.PushStyleColor(g.StyleColorButton, colorButtonBg)
			g.PushStyleColor(g.StyleColorButtonHovered, color.RGBA{50, 60, 80, 255})
			g.PushStyleColor(g.StyleColorButtonActive, color.RGBA{60, 70, 90, 255})

			// Input styling
			g.PushStyleColor(g.StyleColorFrameBg, colorInputBg)
			g.PushStyleColor(g.StyleColorFrameBgHovered, color.RGBA{40, 50, 65, 255})
			g.PushStyleColor(g.StyleColorFrameBgActive, color.RGBA{50, 60, 75, 255})

			// Tab styling
			g.PushStyleColor(g.StyleColorTab, colorTabInactive)
			g.PushStyleColor(g.StyleColorTabActive, colorTabActive)
			g.PushStyleColor(g.StyleColorTabHovered, color.RGBA{80, 90, 110, 200})

			// Header styling
			g.PushStyleColor(g.StyleColorHeader, color.RGBA{40, 50, 70, 150})
			g.PushStyleColor(g.StyleColorHeaderHovered, color.RGBA{50, 60, 80, 200})
			g.PushStyleColor(g.StyleColorHeaderActive, color.RGBA{60, 70, 90, 255})

			// Separator
			g.PushStyleColor(g.StyleColorSeparator, color.RGBA{80, 90, 110, 255})

			// Border
			g.PushStyleColor(g.StyleColorBorder, color.RGBA{60, 70, 90, 255})
		}),

		// Header section
		g.Child().Size(-1, 40).Layout(
			g.Row(
				// CABALBOT title with orange color
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorPrimary)
					g.Label("CABALBOT v2.0")
					g.PopStyleColor()
				}),
				g.Dummy(0, 0), // Spacer to push time to right
				// Current time
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorTextSecondary)
					g.Label(getCurrentDateTime())
					g.PopStyleColor()
				}),
			),
		),

		// Tab bar section
		g.Child().Size(-1, -40).Layout( // Leave space for status bar
			g.TabBar().TabItems(
				g.TabItem("🏠 Main").Layout(renderMainTab()),
				g.TabItem("⚙ Action").Layout(renderActionsTab()),
				g.TabItem("🔧 Config").Layout(renderConfigTab()),
				g.TabItem("🔧 Driver").Layout(renderDriverTab()),
			),
		),

		// Status bar at bottom
		g.Child().Size(-1, 30).Layout(
			g.Row(
				// System status
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorSuccess)
					g.Label("● System Ready")
					g.PopStyleColor()
				}),
				g.Dummy(0, 0), // Spacer
				// System indicators
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorTextSecondary)
					g.Label("📡 87%  💾 24%  🔌 3/4")
					g.PopStyleColor()
				}),
			),
		),

		// Pop all the style colors we pushed (17 total)
		g.Custom(func() {
			for i := 0; i < 17; i++ {
				g.PopStyleColor()
			}
		}),
	}
}

// renderMainTab renders the main tab with instruction list
func renderMainTab() g.Layout {
	return g.Layout{
		g.Child().Size(-1, -1).Layout(
			// Instructions section header
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("📋 Hướng dẫn")
				g.PopStyleColor()
			}),
			g.Separator(),
			g.Spacing(),

			// Instruction list matching target design
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorText)
				g.Label("1. Cài đặt driver interception cho việc chế độ")
				g.PopStyleColor()
			}),
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorText)
				g.Label("2. Cấu hình hành động trong tab Hành Động")
				g.PopStyleColor()
			}),
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorText)
				g.Label("3. Cấu hình phím khóa trong tab Cấu Hình")
				g.PopStyleColor()
			}),
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorText)
				g.Label("4. Nhấn vào nút để bắt đầu chạy hành động")
				g.PopStyleColor()
			}),
			g.Spacing(),
			g.Separator(),
			g.Spacing(),

			// Game section
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("Game")
				g.PopStyleColor()
			}),
			g.Spacing(),

			// Game detection and control buttons
			g.Row(
				g.Button("🔍 Tìm Game").Size(120, 35).OnClick(detectGameProcess),
				g.Custom(func() {
					if state.gameProcess.Found {
						g.PushStyleColor(g.StyleColorButton, colorSuccess)
						g.Button("🟢 Tắt cấu hình").Size(120, 35).OnClick(stopBot)
						g.PopStyleColor()
					} else {
						g.PushStyleColor(g.StyleColorButton, colorDanger)
						g.Button("🔴 Lưu cấu hình").Size(120, 35).OnClick(startBot)
						g.PopStyleColor()
					}
				}),
			),
			g.Spacing(),

			// Overlay toggle
			g.Checkbox("Bật Chế Độ Overlay", &state.overlayEnabled),
		),
	}
}

// renderActionsTab renders the actions tab with action list matching target design
func renderActionsTab() g.Layout {
	return g.Layout{
		g.Child().Size(-1, -1).Layout(
			// Actions section header
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("📋 Chuỗi Hành Động")
				g.PopStyleColor()
			}),
			g.Separator(),
			g.Spacing(),

			// Actions list in a scrollable area
			g.Child().Size(-1, 300).Layout(
				renderActionsList(),
			),

			// Action editor if editing
			renderActionEditor(),
		),
	}
}

// renderActionsList renders the list of actions
func renderActionsList() g.Layout {
	var layout g.Layout

	for i := 0; i < 6; i++ { // Show 6 actions like in target design
		// Ensure we have enough actions in the config
		if i >= len(state.config.Actions) {
			for len(state.config.Actions) <= i {
				state.config.Actions = append(state.config.Actions, BotAction{
					Key:      "",
					ScanCode: 0,
					Duration: 1.0,
					Enabled:  false,
				})
			}
		}

		action := state.config.Actions[i]
		actionIndex := i // Capture for closure

		layout = append(layout, g.Row(
			// Checkbox for enabled/disabled
			g.Checkbox(fmt.Sprintf("##enabled%d", i), &state.config.Actions[i].Enabled),
			// Action name
			g.Label(fmt.Sprintf("Hành động %d", i+1)),
			// Action details or placeholder
			g.Custom(func() {
				if action.Key != "" {
					g.PushStyleColor(g.StyleColorText, colorText)
					g.Label(fmt.Sprintf("Nội dung hành động"))
					g.PopStyleColor()
				} else {
					g.PushStyleColor(g.StyleColorText, colorTextSecondary)
					g.Label("Nội dung hành động")
					g.PopStyleColor()
				}
			}),
			// Edit button with orange color like target
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorButton, colorWarning)
				g.Button(fmt.Sprintf("Sửa##%d", i)).Size(60, 25).OnClick(func() {
					editAction(actionIndex)
				})
				g.PopStyleColor()
			}),
		))
		layout = append(layout, g.Spacing())
	}

	return layout
}

// renderActionEditor renders the action editor when editing
func renderActionEditor() g.Layout {
	if state.editingAction < 0 || state.editingAction >= len(state.config.Actions) {
		return g.Layout{}
	}

	// Get all available keys
	keyNames := getAllKeyNames()

	// Create index for combo selection
	var selectedKeyIndex int32 = 0

	// Find current key index
	for i, key := range keyNames {
		if key == state.tempKey {
			selectedKeyIndex = int32(i)
			break
		}
	}

	return g.Layout{
		g.Spacing(),
		g.Child().Size(-1, -1).Layout(
			// Editor header
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label(fmt.Sprintf("✏ Đang sửa Hành Động %d", state.editingAction+1))
				g.PopStyleColor()
			}),
			g.Separator(),
			g.Spacing(),

			// Key selection
			g.Label("Phím"),
			g.Row(
				g.Combo("##actionkey", state.tempKey, keyNames, &selectedKeyIndex).Size(200).OnChange(func() {
					if selectedKeyIndex >= 0 && int(selectedKeyIndex) < len(keyNames) {
						state.tempKey = keyNames[selectedKeyIndex]
					}
				}),
				g.Button("Thời lượng").OnClick(func() {
					startRecordingKey(&state.tempKey, nil)
				}),
			),
			g.Spacing(),

			// Duration input
			g.Label("Nội dung hành động"),
			g.InputText(&state.tempDuration).Size(200),
			g.Spacing(),

			// Action buttons
			g.Row(
				g.Button("Hủy").Size(80, 30).OnClick(cancelActionEdit),
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorButton, colorPrimary)
					g.Button("Lưu").Size(80, 30).OnClick(saveActionChanges)
					g.PopStyleColor()
				}),
			),
		),
	}
}

// renderConfigTab renders the configuration tab matching target design
func renderConfigTab() g.Layout {
	// Get all available keys
	keyNames := getAllKeyNames()

	// Create index for combo selection
	var selectedKeyIndex int32 = 0

	// Find current key index
	for i, key := range keyNames {
		if key == state.config.LockKey {
			selectedKeyIndex = int32(i)
			break
		}
	}

	// Space key spam interval
	var spaceInterval int32 = int32(state.config.SpaceSpamInterval)

	return g.Layout{
		g.Child().Size(-1, -1).Layout(
			// Config section header
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("⚙ Nhấn phím cho hành động")
				g.PopStyleColor()
			}),
			g.Separator(),
			g.Spacing(),

			// Lock Key Configuration
			g.Label("Chọn phím..."),
			g.Row(
				g.Combo("##lockkey", state.config.LockKey, keyNames, &selectedKeyIndex).Size(200).OnChange(func() {
					if selectedKeyIndex >= 0 && int(selectedKeyIndex) < len(keyNames) {
						setLockKey(keyNames[selectedKeyIndex])
					}
				}),
				g.Button("Nhấn phím cho hành động").OnClick(func() {
					startRecordingKey(&state.config.LockKey, &state.config.LockScanCode)
				}),
			),
			g.Spacing(),

			// Timer Interval Section
			g.Label("Khoảng Thời Gian"),
			g.Row(
				g.Label("Bật spam nhặt đồ"),
				g.Dummy(0, 0), // Spacer
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorPrimary)
					g.Label("Vô hiệu")
					g.PopStyleColor()
				}),
			),
			g.Spacing(),

			// Space Key Spam Section
			g.Label("Khoảng thời gian Space"),
			g.Row(
				g.SliderInt(&spaceInterval, 1, 200).Size(300).OnChange(func() {
					setSpaceSpamInterval(int(spaceInterval))
				}),
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorText, colorPrimary)
					g.Label(fmt.Sprintf("%d", spaceInterval))
					g.PopStyleColor()
				}),
			),
			g.Spacing(),
			g.Separator(),
			g.Spacing(),

			// Game Section
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("Game")
				g.PopStyleColor()
			}),
			g.Spacing(),

			// Game control buttons with progress bar style
			g.Row(
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorButton, colorSecondary)
					g.Button("🔍 Tìm Game").Size(120, 35).OnClick(detectGameProcess)
					g.PopStyleColor()
				}),
				g.Custom(func() {
					g.PushStyleColor(g.StyleColorButton, colorWarning)
					g.Button("🟡 Lưu cấu hình").Size(120, 35).OnClick(saveConfigToFile)
					g.PopStyleColor()
				}),
			),
			g.Spacing(),

			// Overlay toggle
			g.Checkbox("Bật Chế Độ Overlay", &state.overlayEnabled),
		),
	}
}

// renderDriverTab renders the driver installation tab matching target design
func renderDriverTab() g.Layout {
	// Check if driver is installed (simplified check)
	driverInstalled := state.driverStatus == "Driver installed" || state.driverStatus == ""

	return g.Layout{
		g.Child().Size(-1, -1).Layout(
			// Driver section header
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorPrimary)
				g.Label("🔧 Driver Interception")
				g.PopStyleColor()
			}),
			g.Separator(),
			g.Spacing(),

			// Driver Status
			g.Label("Trạng Thái Driver Interception"),
			g.Row(
				g.Custom(func() {
					var checked bool = driverInstalled
					g.Checkbox("##driverstatus", &checked)
				}),
				g.Label("Đã cài đặt Driver"),
			),
			g.Spacing(),

			// Installation Section
			g.Label("Cài Đặt:"),
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorButton, colorSecondary)
				g.Button("📥 Cài Đặt Driver").Size(-1, 35).OnClick(installDriver)
				g.PopStyleColor()
			}),
			g.Spacing(),

			// Status message
			g.Custom(func() {
				if state.installStatus != "" {
					g.Label(state.installStatus)
					g.Spacing()
				}
			}),

			// Warning messages
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorWarning)
				g.Label("Lưu ý: Cài đặt yêu cầu quyền quản trị")
				g.PopStyleColor()
			}),
			g.Custom(func() {
				g.PushStyleColor(g.StyleColorText, colorTextSecondary)
				g.Label("Cần khởi động lại hệ thống")
				g.PopStyleColor()
			}),
		),
	}
}

// Helper functions for action editing
func editAction(index int) {
	state.editingAction = index
	action := state.config.Actions[index]
	state.tempKey = action.Key
	state.tempDuration = fmt.Sprintf("%.1f", action.Duration)
}

func saveActionChanges() {
	if state.editingAction < 0 || state.editingAction >= len(state.config.Actions) {
		return
	}

	// Update the key if changed
	if state.tempKey != "" {
		// Check if it's a valid key
		if scanCode, ok := keyNameToScanCode[state.tempKey]; ok {
			state.config.Actions[state.editingAction].Key = state.tempKey
			state.config.Actions[state.editingAction].ScanCode = scanCode
		}
	}

	// Update the duration if changed
	if state.tempDuration != "" {
		duration, err := strconv.ParseFloat(state.tempDuration, 64)
		if err == nil && duration >= 0.1 && duration <= 10.0 {
			state.config.Actions[state.editingAction].Duration = duration
		}
	}

	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
}

func cancelActionEdit() {
	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CABALBOT v2.0 - <PERSON>r<PERSON><PERSON> Hỗ Trợ Tự Động</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            position: relative;
        }

        .app-container {
            width: 600px;
            height: 400px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: -1;
        }

        .tab-btn.active {
            border-bottom: 3px solid #3B82F6;
            color: white;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .control-btn {
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
        }

        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
    </style>
  <script type="module" crossorigin src="/assets/index.e558a2f9.js"></script>
  <link rel="stylesheet" href="/assets/index.f802a65c.css">
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900 p-4">
    <div id="app"></div>
    
</body>
</html>

package main

import (
	"fmt"
	"strconv"

	g "github.com/AllenDang/giu"
)

// startRecording<PERSON>ey starts recording a key press
func startRecordingKey(targetKey *string, targetScanCode *uint16) {
	state.keyRecordingState.Recording = true
	state.keyRecordingState.TargetKey = targetKey
	state.keyRecordingState.TargetScanCode = targetScanCode
	state.statusMessage = translate("Recording key")
}

// stopRecording<PERSON>ey stops recording a key press
func stopRecordingKey() {
	state.keyRecordingState.Recording = false
	state.keyRecordingState.TargetKey = nil
	state.keyRecordingState.TargetScanCode = nil
}

// moveToNextWizardStep advances to the next wizard step
func moveToNextWizardStep() {
	if state.wizardState.CurrentStep < state.wizardState.TotalSteps {
		state.wizardState.CurrentStep++
	} else {
		state.wizardState.Completed = true
	}
}

// moveToPreviousWizardStep goes back to the previous wizard step
func moveToPreviousWizardStep() {
	if state.wizardState.CurrentStep > 1 {
		state.wizardState.CurrentStep--
	}
}

// renderWizardStep renders the current step of the setup wizard
func renderWizardStep() g.Layout {
	var layout g.Layout

	// Add title and progress
	layout = append(layout, g.Label(translate("Setup Wizard")))
	layout = append(layout, g.ProgressBar(float32(state.wizardState.CurrentStep)/float32(state.wizardState.TotalSteps)).Size(g.Auto, 20))
	layout = append(layout, g.Label(fmt.Sprintf("%s %d/%d", translate("Step"), state.wizardState.CurrentStep, state.wizardState.TotalSteps)))
	layout = append(layout, g.Separator())

	// Render current step content
	switch state.wizardState.CurrentStep {
	case 1:
		// Welcome step
		layout = append(layout, g.Label(translate("Welcome to Cabal Bot")))
		layout = append(layout, g.Label(translate("This wizard will guide you through the setup process.")))
		layout = append(layout, g.Spacing())
		layout = append(layout, g.Label(translate("Driver Installation")))
		layout = append(layout, g.Label(state.driverStatus))
		layout = append(layout, g.Button(translate("Install Driver")).OnClick(installDriver))
		layout = append(layout, g.Label(state.installStatus))

	case 2:
		// Basic configuration
		layout = append(layout, g.Label(translate("Basic Configuration")))
		layout = append(layout, g.Spacing())

		// Lock key selection with direct key recording
		layout = append(layout, g.Label(translate("Lock Key")))
		if state.keyRecordingState.Recording && state.keyRecordingState.TargetKey == &state.config.LockKey {
			layout = append(layout, g.Button(translate("Recording key")).OnClick(stopRecordingKey))
		} else {
			layout = append(layout, g.Row(
				g.Label(state.config.LockKey),
				g.Button(translate("Press any key")).OnClick(func() {
					startRecordingKey(&state.config.LockKey, &state.config.LockScanCode)
				}),
			))
		}

		// Timer interval
		var timerInterval float32 = float32(state.config.TimerInterval)
		layout = append(layout, g.Spacing())
		layout = append(layout, g.Label(fmt.Sprintf("%s: %.1f %s", translate("Timer Interval"), state.config.TimerInterval, translate("seconds"))))
		layout = append(layout, g.SliderFloat(&timerInterval, 0.1, 10.0).OnChange(func() {
			setTimerInterval(float64(timerInterval))
		}))

		// Game detection
		layout = append(layout, g.Spacing())
		layout = append(layout, g.Label(translate("Game Process")))
		if state.gameProcess.Found {
			layout = append(layout, g.Label(fmt.Sprintf("%s: %s", translate("Detected game path"), state.gameProcess.Path)))
		}
		layout = append(layout, g.Button(translate("Detect Game")).OnClick(detectGameProcess))

	case 3:
		// Action configuration
		layout = append(layout, g.Label(translate("Action Configuration")))
		layout = append(layout, g.Spacing())

		// Item collection with space key
		layout = append(layout, g.Label(translate("Item Collection")))
		var spaceEnabled bool = state.config.SpaceSpamEnabled
		layout = append(layout, g.Checkbox(translate("Use Space Key for Item Collection"), &spaceEnabled).OnChange(func() {
			toggleSpaceSpam(spaceEnabled)
		}))

		var spaceInterval int32 = int32(state.config.SpaceSpamInterval)
		layout = append(layout, g.Row(
			g.Label(translate("Item Collection Interval")),
			g.SliderInt(&spaceInterval, 50, 2000).OnChange(func() {
				setSpaceSpamInterval(int(spaceInterval))
			}),
			g.Label(fmt.Sprintf("%d %s", spaceInterval, translate("ms"))),
		))

		// Action sequence
		layout = append(layout, g.Spacing())
		layout = append(layout, g.Label(translate("Action Sequence")))
		layout = append(layout, g.Separator())

		// Only show first 3 actions in wizard for simplicity
		maxActionsInWizard := 3
		if maxActionsInWizard > len(state.config.Actions) {
			maxActionsInWizard = len(state.config.Actions)
		}

		for i := 0; i < maxActionsInWizard; i++ {
			action := state.config.Actions[i]
			actionIndex := i

			layout = append(layout, g.Row(
				g.Checkbox("##enabled"+strconv.Itoa(i), &state.config.Actions[i].Enabled),
				g.Label(fmt.Sprintf("%d. %s: %s, %s: %.1f %s",
					i+1, translate("Key"), action.Key,
					translate("Duration"), action.Duration,
					translate("seconds"))),
				g.Button(fmt.Sprintf("%s##%d", translate("Edit"), i)).OnClick(func() {
					editAction(actionIndex)
				}),
			))
		}

		layout = append(layout, g.Label("..."))
		layout = append(layout, g.Label(translate("Additional actions can be configured in the Actions tab.")))

	case 4:
		// Finish step
		layout = append(layout, g.Label(translate("Finish Setup")))
		layout = append(layout, g.Label(translate("Setup is complete!")))
		layout = append(layout, g.Label(translate("You can now start the bot or adjust additional settings.")))
		layout = append(layout, g.Spacing())
		layout = append(layout, g.Separator())
		layout = append(layout, g.Button(translate("Start Bot")).Size(200, 40).OnClick(startBot))
	}

	// Navigation buttons
	layout = append(layout, g.Spacing())
	layout = append(layout, g.Separator())
	layout = append(layout, g.Row(
		g.Button(translate("Previous")).OnClick(moveToPreviousWizardStep).Disabled(state.wizardState.CurrentStep <= 1),
		g.Button(translate("Next")).OnClick(moveToNextWizardStep).Disabled(state.wizardState.CurrentStep >= state.wizardState.TotalSteps),
		g.Button(translate("Finish")).OnClick(func() {
			state.wizardState.Completed = true
		}),
	))

	return layout
}

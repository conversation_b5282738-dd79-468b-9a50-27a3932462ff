package main

import (
	"log"
	"strings"

	hook "github.com/robotn/gohook"
)

// Global hook event channel
var (
	hookEventChannel chan hook.Event
	hookStarted      bool
	keyCodeToName    map[uint16]string
)

// Initialize the keyboard hook
func initKeyboardHook() {
	// Initialize key code to name mapping
	initKeyCodeMap()

	// Check if hook is already started
	if hookStarted {
		return
	}

	// Start the keyboard hook
	hookEventChannel = hook.Start()
	hookStarted = true

	// Process hook events in a goroutine
	go processHookEvents()

	log.Println("Keyboard hook initialized")
}

// Initialize the key code to name mapping for reverse lookups
func initKeyCodeMap() {
	keyCodeToName = make(map[uint16]string)

	// Populate the map with reverse mappings from scanCode to name
	for name, scanCode := range keyNameToScanCode {
		keyCodeToName[scanCode] = name
	}
}

// Process hook events from the hook channel
func processHookEvents() {
	for event := range hookEventChannel {
		// We only care about key down events for recording
		if event.Kind == hook.KeyDown && state.keyRecordingState.Recording {
			// Convert hook key code to our scan code format
			scanCode := convertHookToScanCode(event.Rawcode)

			// Get key name from scanCode
			keyName := getKeyNameFromCode(scanCode)

			if keyName != "" {
				// Process the keyboard input
				processKeyboardInput(keyName, scanCode)
			}
		}
	}
}

// Convert hook key code to our scan code format
func convertHookToScanCode(hookCode uint16) uint16 {
	// Many key codes should be the same, but some may need conversion
	// You might need to adjust this conversion based on actual key codes
	return hookCode
}

// Get key name from scan code
func getKeyNameFromCode(scanCode uint16) string {
	// Look up the key name from the code
	if name, ok := keyCodeToName[scanCode]; ok {
		return name
	}

	// If not found directly, try to construct a name
	// For example, convert ASCII for printable characters
	if scanCode >= 32 && scanCode <= 126 {
		return string(rune(scanCode))
	}

	// Special handling for common keys not in our mapping
	switch scanCode {
	case 13:
		return "ENTER"
	case 27:
		return "ESC"
	case 32:
		return "SPACE"
	}

	// Handle function keys
	if scanCode >= 112 && scanCode <= 123 {
		return "F" + string(rune(scanCode-111+'0'))
	}

	// Return uppercase letter for ASCII codes
	if scanCode >= 65 && scanCode <= 90 {
		return strings.ToUpper(string(rune(scanCode)))
	}

	// If we still can't determine the key, just return an empty string
	return ""
}

// Cleanup the keyboard hook when the application exits
func cleanupKeyboardHook() {
	if hookStarted {
		hook.End()
		hookStarted = false
		log.Println("Keyboard hook cleaned up")
	}
}

// Register a key combination handler
func registerKeyCombination(keys []string, handler func()) {
	// Convert key names to lowercase for consistent handling
	lowerKeys := make([]string, len(keys))
	for i, key := range keys {
		lowerKeys[i] = strings.ToLower(key)
	}

	// Register the key combination
	hook.Register(hook.KeyDown, lowerKeys, func(e hook.Event) {
		handler()
	})
}

{"name": "vite", "version": "3.2.11", "type": "module", "license": "MIT", "author": "<PERSON>", "description": "Native-ESM powered web dev build tool", "bin": {"vite": "bin/vite.js"}, "main": "./dist/node/index.js", "module": "./dist/node/index.js", "types": "./dist/node/index.d.ts", "exports": {".": {"types": "./dist/node/index.d.ts", "import": "./dist/node/index.js", "require": "./index.cjs"}, "./client": {"types": "./client.d.ts"}, "./dist/client/*": "./dist/client/*", "./package.json": "./package.json"}, "files": ["bin", "dist", "client.d.ts", "index.cjs", "types"], "engines": {"node": "^14.18.0 || >=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/#readme", "//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "dependencies": {"esbuild": "^0.15.9", "postcss": "^8.4.18", "resolve": "^1.22.1", "rollup": "^2.79.1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "devDependencies": {"@ampproject/remapping": "^2.2.0", "@babel/parser": "^7.20.0", "@babel/types": "^7.20.0", "@jridgewell/trace-mapping": "^0.3.17", "@rollup/plugin-alias": "^4.0.2", "@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-dynamic-import-vars": "^2.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "14.1.0", "@rollup/plugin-typescript": "^8.5.0", "@rollup/pluginutils": "^4.2.1", "@types/escape-html": "^1.0.0", "acorn": "^8.8.1", "acorn-walk": "^8.2.0", "cac": "^6.7.14", "chokidar": "^3.5.3", "connect": "^3.7.0", "connect-history-api-fallback": "^2.0.0", "convert-source-map": "^1.9.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dep-types": "link:./src/types", "dotenv": "^14.3.2", "dotenv-expand": "^5.1.0", "es-module-lexer": "^1.1.0", "escape-html": "^1.0.3", "estree-walker": "^3.0.1", "etag": "^1.8.1", "fast-glob": "^3.2.12", "http-proxy": "^1.18.1", "json5": "^2.2.1", "launch-editor-middleware": "^2.6.0", "magic-string": "^0.26.7", "micromatch": "^4.0.5", "mlly": "^0.5.16", "mrmime": "^1.0.1", "okie": "^1.0.1", "open": "^8.4.0", "parse5": "^7.1.1", "periscopic": "^3.0.4", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "postcss-import": "^15.0.0", "postcss-load-config": "^4.0.1", "postcss-modules": "^6.0.0", "resolve.exports": "^1.1.0", "sirv": "^2.0.2", "source-map-js": "^1.0.2", "source-map-support": "^0.5.21", "strip-ansi": "^7.0.1", "strip-literal": "^0.4.2", "tsconfck": "^2.0.1", "tslib": "^2.4.0", "types": "link:./types", "ufo": "^0.8.6", "ws": "^8.10.0"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "less": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}, "scripts": {"dev": "rimraf dist && pnpm run build-bundle -w", "build": "rimraf dist && run-s build-bundle build-types", "build-bundle": "rollup --config rollup.config.ts --configPlugin typescript", "build-types": "run-s build-types-temp build-types-pre-patch build-types-roll build-types-post-patch build-types-check", "build-types-temp": "tsc --emitDeclarationOnly --outDir temp/node -p src/node", "build-types-pre-patch": "tsx scripts/prePatchTypes.ts", "build-types-roll": "api-extractor run && rimraf temp", "build-types-post-patch": "tsx scripts/postPatchTypes.ts", "build-types-check": "tsc --project tsconfig.check.json", "lint": "eslint --cache --ext .ts src/**", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\""}}
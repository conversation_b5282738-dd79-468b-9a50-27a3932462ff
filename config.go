package main

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
)

// Mapping of key names to scan codes
var keyNameToScanCode = map[string]uint16{
	"ESC":   SCANCODE_ESC,
	"1":     SCANCODE_1,
	"2":     SCANCODE_2,
	"3":     SCANCODE_3,
	"4":     SCANCODE_4,
	"5":     SCANCODE_5,
	"6":     SCANCODE_6,
	"7":     SCANCODE_7,
	"8":     SCANCODE_8,
	"9":     SCANCODE_9,
	"0":     SCANCODE_0,
	"-":     SCANCODE_MINUS,
	"+":     SCANCODE_PLUS,
	"TAB":   <PERSON><PERSON><PERSON><PERSON>_TAB,
	"Q":     SCANCODE_Q,
	"W":     SCANCO<PERSON>_W,
	"E":     SCAN<PERSON><PERSON>_<PERSON>,
	"R":     SCANCODE_R,
	"T":     SCANCODE_T,
	"Y":     SCANCODE_Y,
	"U":     SCANCODE_U,
	"I":     SCAN<PERSON>DE_I,
	"O":     <PERSON><PERSON><PERSON><PERSON>_O,
	"P":     SCANCODE_P,
	"A":     SCANCODE_A,
	"S":     <PERSON>AN<PERSON>DE_S,
	"D":     <PERSON>AN<PERSON><PERSON>_D,
	"F":     SCAN<PERSON><PERSON>_F,
	"G":     <PERSON><PERSON><PERSON><PERSON>_G,
	"H":     SCANCODE_H,
	"J":     SCANCODE_J,
	"K":     SCANCODE_K,
	"L":     SCANCODE_L,
	"Z":     SCANCODE_Z,
	"X":     SCANCODE_X,
	"C":     SCANCODE_C,
	"V":     SCANCODE_V,
	"B":     SCANCODE_B,
	"N":     SCANCODE_N,
	"M":     SCANCODE_M,
	"SPACE": SCANCODE_SPACE,
	"ALT":   SCANCODE_ALT,
	"CTRL":  SCANCODE_CTRL,
	"SHIFT": SCANCODE_SHIFT,
	"F1":    SCANCODE_F1,
	"F2":    SCANCODE_F2,
	"F3":    SCANCODE_F3,
	"F4":    SCANCODE_F4,
	"F5":    SCANCODE_F5,
	"F6":    SCANCODE_F6,
	"F7":    SCANCODE_F7,
	"F8":    SCANCODE_F8,
	"F9":    SCANCODE_F9,
	"F10":   SCANCODE_F10,
	"F11":   SCANCODE_F11,
	"F12":   SCANCODE_F12,
}

// Get array of all available key names
func getAllKeyNames() []string {
	keys := make([]string, 0, len(keyNameToScanCode))
	for k := range keyNameToScanCode {
		keys = append(keys, k)
	}
	// Sort alphabetically for better display
	sort.Strings(keys)
	return keys
}

// BotConfigSerialized is a serializable version of BotConfig without channels and mutexes
type BotConfigSerialized struct {
	Actions            []BotAction `json:"actions"`
	TimerInterval      float64     `json:"timerInterval"`
	LockKey            string      `json:"lockKey"`
	LockScanCode       uint16      `json:"lockScanCode"`
	SpaceSpamEnabled   bool        `json:"spaceSpamEnabled"`
	SpaceSpamInterval  int         `json:"spaceSpamInterval"`
	Running            bool        `json:"running"`
	OverlayEnabled     bool        `json:"overlayEnabled"`
	OverlayPosition    string      `json:"overlayPosition"`
	Transparency       float32     `json:"transparency"`
	DirectInputEnabled bool        `json:"directInputEnabled"`
}

// SaveConfig saves the bot configuration to a file
func SaveConfig(config *BotConfig, filename string) error {
	config.Mutex.Lock()
	defer config.Mutex.Unlock()

	// Create a serializable version of the config
	configSerialized := &BotConfigSerialized{
		Actions:            config.Actions,
		TimerInterval:      config.TimerInterval,
		LockKey:            config.LockKey,
		LockScanCode:       config.LockScanCode,
		SpaceSpamEnabled:   config.SpaceSpamEnabled,
		SpaceSpamInterval:  config.SpaceSpamInterval,
		Running:            false, // Don't save running state
		OverlayEnabled:     state.overlayEnabled,
		OverlayPosition:    state.overlayPosition,
		Transparency:       state.transparency,
		DirectInputEnabled: state.directInputEnabled,
	}

	// Convert to JSON
	data, err := json.MarshalIndent(configSerialized, "", "  ")
	if err != nil {
		return err
	}

	// Write to file
	return os.WriteFile(filename, data, 0644)
}

// LoadConfig loads the bot configuration from a file
func LoadConfig(config *BotConfig, filename string) error {
	// Read file
	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	// Create a temporary serializable config
	var configSerialized BotConfigSerialized

	// Parse JSON
	err = json.Unmarshal(data, &configSerialized)
	if err != nil {
		return err
	}

	// Update the current config
	config.Mutex.Lock()
	defer config.Mutex.Unlock()

	config.Actions = configSerialized.Actions
	config.TimerInterval = configSerialized.TimerInterval
	config.LockKey = configSerialized.LockKey
	config.LockScanCode = configSerialized.LockScanCode
	config.SpaceSpamEnabled = configSerialized.SpaceSpamEnabled
	config.SpaceSpamInterval = configSerialized.SpaceSpamInterval
	// Don't update Running status, StopChan, or SpaceStopChan

	// Load overlay settings if they exist
	if configSerialized.OverlayEnabled {
		state.overlayEnabled = configSerialized.OverlayEnabled
	}
	if configSerialized.OverlayPosition != "" {
		state.overlayPosition = configSerialized.OverlayPosition
	}
	if configSerialized.Transparency > 0 {
		state.transparency = configSerialized.Transparency
	}
	state.directInputEnabled = configSerialized.DirectInputEnabled

	return nil
}

// NewBotConfig creates a new bot configuration with default values
func NewBotConfig() *BotConfig {
	config := &BotConfig{
		Actions:           make([]BotAction, 10),
		TimerInterval:     1.0,
		LockKey:           "Z",
		LockScanCode:      SCANCODE_Z,
		SpaceSpamEnabled:  false,
		SpaceSpamInterval: 200, // Default 200ms interval
		Running:           false,
	}

	// Initialize actions with default values
	for i := range config.Actions {
		config.Actions[i] = BotAction{
			Key:      "Z",
			ScanCode: SCANCODE_Z,
			Duration: 1.0,
			Enabled:  false,
		}
	}

	// Enable first action by default
	if len(config.Actions) > 0 {
		config.Actions[0].Enabled = true
	}

	return config
}

// setLockKey sets the lock key
func setLockKey(key string) {
	// Check if it's a valid key
	if scanCode, ok := keyNameToScanCode[key]; ok {
		state.config.LockKey = key
		state.config.LockScanCode = scanCode
		state.statusMessage = fmt.Sprintf("%s: %s", translate("Lock key set to"), key)
	} else {
		state.statusMessage = translate("Invalid key selected!")
	}
}

// setTimerInterval sets the timer interval
func setTimerInterval(interval float64) {
	if interval < 0.1 || interval > 10.0 {
		state.statusMessage = translate("Interval must be between 0.1 and 10.0 seconds.")
		return
	}
	state.config.TimerInterval = interval
	state.statusMessage = fmt.Sprintf("%s: %.1f %s",
		translate("Interval set to"), interval,
		translate("seconds"))
}

// setSpaceSpamInterval sets the interval for space key spam
func setSpaceSpamInterval(interval int) {
	if interval < 50 || interval > 2000 {
		state.statusMessage = translate("Interval must be between 50 and 2000 milliseconds.")
		return
	}

	state.config.Mutex.Lock()
	state.config.SpaceSpamInterval = interval
	state.config.Mutex.Unlock()
}

// saveConfigToFile saves the configuration to a file
func saveConfigToFile() {
	err := SaveConfig(state.config, "bot_config.json")
	if err != nil {
		state.statusMessage = fmt.Sprintf("%s: %v", translate("Error saving config"), err)
	} else {
		state.statusMessage = translate("Configuration saved successfully!")
	}
}

// loadConfigFromFile loads the configuration from a file
func loadConfigFromFile() {
	err := LoadConfig(state.config, "bot_config.json")
	if err != nil {
		state.statusMessage = fmt.Sprintf("%s: %v", translate("Error loading config"), err)
	} else {
		state.statusMessage = translate("Configuration loaded successfully!")
	}
}

// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CompleteWizard():Promise<string>;

export function DetectGame():Promise<Record<string, any>>;

export function GetAppState():Promise<Record<string, any>>;

export function GetAvailableKeys():Promise<Array<string>>;

export function GetConfig():Promise<Record<string, any>>;

export function GetWizardState():Promise<Record<string, any>>;

export function InstallDriver():Promise<string>;

export function LoadConfig():Promise<string>;

export function NextWizardStep():Promise<Record<string, any>>;

export function PreviousWizardStep():Promise<Record<string, any>>;

export function RemoveCabalAlwaysOnTop():Promise<string>;

export function SaveConfig():Promise<string>;

export function SetCabalAlwaysOnTop():Promise<string>;

export function SetLockKey(arg1:string):Promise<string>;

export function SetSpaceSpamInterval(arg1:number):Promise<string>;

export function SetTimerInterval(arg1:number):Promise<string>;

export function StartBot():Promise<string>;

export function StartKeyRecording():Promise<string>;

export function StopBot():Promise<string>;

export function StopKeyRecording():Promise<string>;

export function ToggleOverlay(arg1:boolean):Promise<string>;

export function ToggleSpaceSpam(arg1:boolean):Promise<string>;

export function UpdateAction(arg1:number,arg2:string,arg3:number,arg4:boolean):Promise<string>;

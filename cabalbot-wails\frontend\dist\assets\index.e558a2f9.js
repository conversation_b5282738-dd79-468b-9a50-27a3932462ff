(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const l of o)if(l.type==="childList")for(const d of l.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&s(d)}).observe(document,{childList:!0,subtree:!0});function n(o){const l={};return o.integrity&&(l.integrity=o.integrity),o.referrerpolicy&&(l.referrerPolicy=o.referrerpolicy),o.crossorigin==="use-credentials"?l.credentials="include":o.crossorigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function s(o){if(o.ep)return;o.ep=!0;const l=n(o);fetch(o.href,l)}})();function $(){return ObfuscatedCall(1,[])}function P(){return ObfuscatedCall(4,[])}function G(){return ObfuscatedCall(6,[])}function H(){return ObfuscatedCall(10,[])}function F(){return ObfuscatedCall(11,[])}function R(){return ObfuscatedCall(12,[])}function M(t){return ObfuscatedCall(14,[t])}function z(){return ObfuscatedCall(16,[])}function m(){return ObfuscatedCall(18,[])}function q(t){return ObfuscatedCall(21,[t])}function U(t,e,n,s){return ObfuscatedCall(22,[t,e,n,s])}let a={config:null,gameDetected:!1,botRunning:!1,botPaused:!1,currentTab:"main",editingAction:-1};function i(t,e="info"){const n=document.querySelector(".notification");n&&n.remove();const s=document.createElement("div");switch(s.className="notification fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 transition-all duration-300",e){case"success":s.classList.add("bg-green-600");break;case"error":s.classList.add("bg-red-600");break;case"warning":s.classList.add("bg-yellow-600");break;default:s.classList.add("bg-blue-600")}s.textContent=t,document.body.appendChild(s),setTimeout(()=>{s.parentNode&&(s.style.opacity="0",setTimeout(()=>{s.parentNode&&s.remove()},300))},3e3)}document.addEventListener("DOMContentLoaded",function(){console.log("DOM loaded, initializing app..."),j(),W()});async function j(){console.log("Initializing app..."),X(),await w(),f(),setInterval(f,6e4),console.log("App initialized")}function W(){document.addEventListener("keydown",function(t){if(t.code==="Space"&&t.target.tagName!=="INPUT"&&t.target.tagName!=="TEXTAREA")return t.preventDefault(),t.stopPropagation(),console.log("Space key scroll prevented"),!1;if(t.code==="F5"||t.ctrlKey&&t.code==="KeyR")return t.preventDefault(),t.stopPropagation(),console.log("Page refresh prevented"),!1;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(t.code)&&t.target.tagName!=="INPUT"&&t.target.tagName!=="TEXTAREA")return t.preventDefault(),t.stopPropagation(),!1},!0),document.addEventListener("keyup",function(t){if(t.code==="Space"&&t.target.tagName!=="INPUT"&&t.target.tagName!=="TEXTAREA")return t.preventDefault(),t.stopPropagation(),!1},!0),document.addEventListener("keypress",function(t){if(t.code==="Space"&&t.target.tagName!=="INPUT"&&t.target.tagName!=="TEXTAREA")return t.preventDefault(),t.stopPropagation(),!1},!0),document.addEventListener("contextmenu",function(t){t.preventDefault()}),document.addEventListener("wheel",function(t){return t.preventDefault(),t.stopPropagation(),!1},{passive:!1}),console.log("Keyboard event handlers set up")}function X(){console.log("Rendering app..."),document.body.classList.add("no-space-scroll");const t=`
        <div class="app-container">
            <!-- Background Image -->
            <div class="absolute inset-0 z-0" style="background-image: url('https://i.ibb.co/67P9MrGT/auto-bgr.png'); background-size: cover; background-position: center;"></div>
            <div class="overlay"></div>

            <!-- Main Content -->
            <div class="relative z-10 h-full flex flex-col">
                <!-- Header with Logo -->
                <div class="flex items-center justify-between px-4 py-2 bg-black bg-opacity-30">
                    <div class="flex items-center">
                        <h1 class="text-white font-bold text-xl">CABAL<strong class="text-blue-400">BOT</strong> v2.0</h1>
                    </div>
                    <div class="text-xs text-gray-300">
                        <span id="datetime"></span>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="flex border-b border-gray-700 bg-black bg-opacity-40">
                    <button class="tab-btn active flex-1 py-3 px-4 text-white font-medium text-sm tracking-wider" data-tab="main">
                        <i class="fas fa-home mr-2"></i> Main
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="action">
                        <i class="fas fa-play-circle mr-2"></i> Action
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="config">
                        <i class="fas fa-cog mr-2"></i> Config
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="window">
                        <i class="fas fa-window-maximize mr-2"></i> Window
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="driver">
                        <i class="fas fa-microchip mr-2"></i> Driver
                    </button>
                </div>

                <!-- Tab Contents -->
                <div class="flex-1 overflow-y-auto p-4 bg-black bg-opacity-30" id="tab-container">
                    <!-- Tabs will be rendered here -->
                </div>

                <!-- Status Bar -->
                <div class="bg-black bg-opacity-60 text-xs text-gray-400 px-4 py-2 flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <span>System Ready</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span><i class="fas fa-satellite-dish mr-1"></i> 87%</span>
                        <span><i class="fas fa-database mr-1"></i> 24%</span>
                        <span><i class="fas fa-plug mr-1"></i> 3/4</span>
                    </div>
                </div>
            </div>
        </div>
    `;document.querySelector("#app").innerHTML=t,c(),_(),console.log("App rendered and event listeners set up")}function c(){const t=document.getElementById("tab-container");!t||(t.innerHTML=`
        ${Z()}
        ${V()}
        ${Q()}
        ${J()}
        ${Y()}
    `,setTimeout(()=>{const e=document.getElementById("lockKey");e&&g(e)},50))}function Z(){return`
        <!-- Main Tab -->
        <div id="main" class="tab-content active text-white">
            <div class="grid grid-cols-1 gap-4">
                <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                    <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                        <i class="fas fa-terminal mr-2"></i> \u0110i\u1EC1u Khi\u1EC3n Bot cabal
                    </h3>
                    <div class="grid grid-cols-3 gap-3">
                        <button id="startBtn" class="control-btn bg-blue-600 hover:bg-blue-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-play text-xl mb-1"></i>
                            <span class="text-xs">Start</span>
                        </button>
                        <button id="pauseBtn" class="control-btn bg-yellow-600 hover:bg-yellow-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-pause text-xl mb-1"></i>
                            <span class="text-xs">Pause</span>
                        </button>
                        <button id="stopBtn" class="control-btn bg-red-600 hover:bg-red-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-stop text-xl mb-1"></i>
                            <span class="text-xs">Stop</span>
                        </button>
                        <div id="gameStatus" class="text-xs text-red-400 text-center col-span-3">Kh\xF4ng t\xECm th\u1EA5y game</div>
                    </div>
                </div>

                <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                    <h3 class="font-bold text-blue-400 mb-2 flex items-center">
                        <i class="fas fa-info-circle mr-2"></i> H\u01B0\u1EDBng d\u1EABn
                    </h3>
                    <div class="text-xs space-y-2 mt-4">
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">1.</span>
                            <span class="text-gray-300">C\xE0i \u0111\u1EB7t driver Interception n\u1EBFu ch\u01B0a c\xE0i \u0111\u1EB7t.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">2.</span>
                            <span class="text-gray-300">C\u1EA5u h\xECnh h\xE0nh \u0111\u1ED9ng trong tab H\xE0nh \u0110\u1ED9ng.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">3.</span>
                            <span class="text-gray-300">C\u1EA5u h\xECnh ph\xEDm kh\xF3a trong tab C\u1EA5u H\xECnh.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">4.</span>
                            <span class="text-gray-300">Nh\u1EA5n <span class="text-green-400">B\u1EAFt \u0110\u1EA7u Bot</span> \u0111\u1EC3 b\u1EAFt \u0111\u1EA7u chu\u1ED7i h\xE0nh \u0111\u1ED9ng.</span>
                        </div>
                    </div>

                    <h3 class="font-bold text-blue-400 mt-4 mb-2">Game</h3>
                    <button id="detectGameBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-1">
                        <i class="fas fa-search mr-1"></i> T\xECm Game
                    </button>
                    <div id="gameDetectionStatus" class="text-xs text-gray-400 text-center">Kh\xF4ng t\xECm th\u1EA5y game</div>
                </div>
            </div>
        </div>
    `}function V(){let t="";if(a.config&&a.config.actions)for(let n=0;n<10;n++){const s=a.config.actions[n]||{key:"",duration:1,enabled:!1};t+=`
                <div class="flex items-center">
                    <div class="w-1/2 flex items-center">
                        <input type="checkbox" ${s.enabled?"checked":""}
                               data-action-index="${n}"
                               class="action-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                        <span class="text-sm text-gray-300">H\xE0nh \u0111\u1ED9ng ${n+1}</span>
                    </div>
                    <div class="w-1/2 px-3 text-sm text-gray-300">${s.key||"Ch\u01B0a c\u1EA5u h\xECnh"} - ${s.duration}s</div>
                    <button data-action-index="${n}" class="edit-action-btn bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">S\u1EEDa</button>
                </div>
            `}else for(let n=0;n<10;n++)t+=`
                <div class="flex items-center">
                    <div class="w-1/2 flex items-center">
                        <input type="checkbox" data-action-index="${n}"
                               class="action-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                        <span class="text-sm text-gray-300">H\xE0nh \u0111\u1ED9ng ${n+1}</span>
                    </div>
                    <div class="w-1/2 px-3 text-sm text-gray-300">Ch\u01B0a c\u1EA5u h\xECnh</div>
                    <button data-action-index="${n}" class="edit-action-btn bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">S\u1EEDa</button>
                </div>
            `;const e=a.editingAction>=0?`
        <!-- Editing Panel -->
        <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4 mt-4">
            <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                <i class="fas fa-edit mr-2"></i> \u0110ang s\u1EEDa H\xE0nh \u0110\u1ED9ng ${a.editingAction+1}
            </h3>
            <div class="grid grid-cols-1 gap-3">
                <div>
                    <label class="block text-sm text-gray-300 mb-1">Ph\xEDm</label>
                    <input type="text" id="editKey" placeholder="Nh\u1EA5n ph\xEDm..." maxlength="1"
                           class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-center">
                </div>
                <div>
                    <label class="block text-sm text-gray-300 mb-1">Th\u1EDDi l\u01B0\u1EE3ng (gi\xE2y)</label>
                    <input type="number" id="editDuration" step="0.1" min="0.1" placeholder="1.0"
                           class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex justify-end space-x-2">
                    <button id="cancelEditBtn" class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">H\u1EE7y</button>
                    <button id="saveActionBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">L\u01B0u</button>
                </div>
            </div>
        </div>
    `:"";return`
        <!-- Action Tab -->
        <div id="action" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-list-ul mr-2"></i> Chu\u1ED7i H\xE0nh \u0110\u1ED9ng
                </h3>
                <div class="space-y-3 mt-4">
                    ${t}
                </div>
            </div>
            ${e}
        </div>
    `}function Q(){const t=a.config||{};return`
        <!-- Config Tab -->
        <div id="config" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-keyboard mr-2"></i> C\u1EA5u H\xECnh Ph\xEDm
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">Ph\xEDm Kh\xF3a</label>
                        <input type="text" id="lockKey" value="${t.lockKey||"Z"}" placeholder="Z" maxlength="1"
                               class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 text-center">
                    </div>
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="text-sm text-gray-300">Kho\u1EA3ng Th\u1EDDi Gian</label>
                            <span id="intervalDisplay" class="text-xs text-blue-400">${t.timerInterval||1} gi\xE2y</span>
                        </div>
                        <div class="flex gap-2">
                            <input type="range" id="intervalSlider" min="0.5" max="3" step="0.1" value="${t.timerInterval||1}"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">Spam nh\u1EB7t \u0111\u1ED3</label>
                        <div class="flex gap-2">
                            <div class="w-full relative flex-1">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" id="spaceSpamEnabled" ${t.spaceSpamEnabled?"checked":""}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">B\u1EADt spam nh\u1EB7t \u0111\u1ED3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="text-sm text-gray-300">Kho\u1EA3ng th\u1EDDi gian Space</label>
                            <span id="spaceIntervalDisplay" class="text-xs text-blue-400">${t.spaceSpamInterval||200}</span>
                        </div>
                        <div class="flex gap-2">
                            <input type="range" id="spaceIntervalSlider" min="50" max="500" step="10" value="${t.spaceSpamInterval||200}"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>
                    <div>
                        <h3 class="font-bold text-blue-400 mb-2">Game</h3>
                        <button id="detectGameConfigBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-3">
                            <i class="fas fa-search mr-1"></i> T\xECm Game
                        </button>
                        <div class="flex gap-2 mb-4">
                            <button id="saveConfigBtn" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm">
                                <i class="fas fa-save mr-1"></i> L\u01B0u c\u1EA5u h\xECnh
                            </button>
                            <button id="loadConfigBtn" class="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded text-sm">
                                <i class="fas fa-download mr-1"></i> T\u1EA3i c\u1EA5u h\xECnh
                            </button>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="overlayMode" ${t.overlayEnabled?"checked":""}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                            <label for="overlayMode" class="text-sm text-gray-300">B\u1EADt Ch\u1EBF \u0110\u1ED9 Overlay</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `}function J(){return`
        <!-- Window Tab -->
        <div id="window" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-window-maximize mr-2"></i> Qu\u1EA3n L\xFD C\u1EEDa S\u1ED5 Game
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">\u0110i\u1EC1u Khi\u1EC3n C\u1EEDa S\u1ED5 Cabal</label>
                        <div class="grid grid-cols-1 gap-3">
                            <button id="setAlwaysOnTopBtn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm">
                                <i class="fas fa-arrow-up mr-1"></i> \u0110\u1EB7t Game Lu\xF4n \u1EDE Tr\xEAn
                            </button>
                            <button id="removeAlwaysOnTopBtn" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded text-sm">
                                <i class="fas fa-arrow-down mr-1"></i> B\u1ECF Lu\xF4n \u1EDE Tr\xEAn
                            </button>
                        </div>
                    </div>

                    <div id="windowStatus" class="text-xs text-gray-400"></div>

                    <div class="bg-blue-900 bg-opacity-30 rounded-lg p-3">
                        <h4 class="font-bold text-blue-300 mb-2 flex items-center">
                            <i class="fas fa-info-circle mr-2"></i> Th\xF4ng Tin
                        </h4>
                        <div class="text-xs text-gray-300 space-y-2">
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">\u2022</span>
                                <span>Khi bot ch\u1EA1y, game s\u1EBD t\u1EF1 \u0111\u1ED9ng \u0111\u01B0\u1EE3c \u0111\u1EB7t lu\xF4n \u1EDF tr\xEAn.</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">\u2022</span>
                                <span>\u0110i\u1EC1u n\xE0y gi\xFAp bot ho\u1EA1t \u0111\u1ED9ng m\xE0 kh\xF4ng c\u1EA7n click v\xE0o game.</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">\u2022</span>
                                <span>B\u1EA1n c\xF3 th\u1EC3 t\u1EAFt t\xEDnh n\u0103ng n\xE0y b\u1EB1ng n\xFAt "B\u1ECF Lu\xF4n \u1EDE Tr\xEAn".</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `}function Y(){return`
        <!-- Driver Tab -->
        <div id="driver" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-microchip mr-2"></i> Driver Interception
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Tr\u1EA1ng Th\xE1i Driver Interception</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="driverStatus" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3" disabled>
                            <span class="text-sm text-gray-300">\u0110\xE3 c\xE0i \u0111\u1EB7t Driver</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">C\xE0i \u0110\u1EB7t:</label>
                        <button id="installDriverBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm">
                            <i class="fas fa-download mr-1"></i> C\xE0i \u0110\u1EB7t Driver
                        </button>
                    </div>

                    <div id="installStatus" class="text-xs text-gray-400"></div>

                    <div class="text-xs text-yellow-400">
                        L\u01B0u \xFD: C\xE0i \u0111\u1EB7t y\xEAu c\u1EA7u quy\u1EC1n qu\u1EA3n tr\u1ECB
                    </div>
                    <div class="text-xs text-gray-400">
                        C\u1EA7n kh\u1EDFi \u0111\u1ED9ng l\u1EA1i h\u1EC7 th\u1ED1ng
                    </div>
                </div>
            </div>
        </div>
    `}function _(){console.log("Setting up event listeners...");const t=document.querySelectorAll(".tab-btn");console.log("Found tab buttons:",t.length),t.forEach(e=>{e.addEventListener("click",n=>{n.preventDefault();const s=e.getAttribute("data-tab");console.log("Tab clicked:",s),r(s)})}),document.addEventListener("click",tt),document.addEventListener("change",et),document.addEventListener("input",nt),window.addEventListener("resize",handleWindowResize),console.log("Event listeners set up")}function tt(t){const e=t.target;if(e.id==="startBtn")t.preventDefault(),console.log("Start button clicked"),y();else if(e.id==="pauseBtn")t.preventDefault(),console.log("Pause button clicked"),v();else if(e.id==="stopBtn")t.preventDefault(),console.log("Stop button clicked"),x();else if(e.id==="detectGameBtn"||e.id==="detectGameConfigBtn")t.preventDefault(),console.log("Detect game button clicked"),h();else if(e.id==="installDriverBtn")t.preventDefault(),console.log("Install driver button clicked"),O();else if(e.id==="saveConfigBtn")t.preventDefault(),console.log("Save config button clicked"),k();else if(e.id==="loadConfigBtn")t.preventDefault(),console.log("Load config button clicked"),C();else if(e.classList.contains("edit-action-btn")){t.preventDefault();const n=parseInt(e.getAttribute("data-action-index"));console.log("Edit action button clicked:",n),B(n)}else e.id==="saveActionBtn"?(t.preventDefault(),console.log("Save action button clicked"),L()):e.id==="cancelEditBtn"?(t.preventDefault(),console.log("Cancel edit button clicked"),E()):e.id==="setAlwaysOnTopBtn"?(t.preventDefault(),console.log("Set always on top button clicked"),N()):e.id==="removeAlwaysOnTopBtn"&&(t.preventDefault(),console.log("Remove always on top button clicked"),K())}function et(t){const e=t.target;if(e.classList.contains("action-checkbox")){const n=parseInt(e.getAttribute("data-action-index"));console.log("Action checkbox changed:",n,e.checked),S(n,e.checked)}else e.id==="spaceSpamEnabled"?(console.log("Space spam checkbox changed:",e.checked),A(e.checked)):e.id==="overlayMode"&&(console.log("Overlay checkbox changed:",e.checked),D(e.checked))}function nt(t){const e=t.target;e.id==="intervalSlider"?(console.log("Interval slider changed:",e.value),T(e.value)):e.id==="spaceIntervalSlider"&&(console.log("Space interval slider changed:",e.value),I(e.value))}function r(t){document.querySelectorAll(".tab-btn").forEach(l=>{l.classList.remove("active","text-white"),l.classList.add("text-gray-300")});const n=document.querySelector(`[data-tab="${t}"]`);n&&(n.classList.add("active","text-white"),n.classList.remove("text-gray-300")),document.querySelectorAll(".tab-content").forEach(l=>{l.style.display="none",l.classList.remove("active")});const o=document.getElementById(t);o&&(o.style.display="block",o.classList.add("active")),a.currentTab=t,t==="config"&&setTimeout(()=>{const l=document.getElementById("lockKey");l&&g(l)},50)}function f(){const t=new Date,e=document.getElementById("datetime");e&&(e.textContent=t.toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}))}async function y(){try{i("\u0110ang kh\u1EDFi \u0111\u1ED9ng bot...","info");const t=await z();console.log("Bot started:",t),a.botRunning=!0,a.botPaused=!1,u(),i("Bot \u0111\xE3 \u0111\u01B0\u1EE3c kh\u1EDFi \u0111\u1ED9ng!","success")}catch(t){console.error("Failed to start bot:",t),i("L\u1ED7i khi kh\u1EDFi \u0111\u1ED9ng bot: "+t.message,"error")}}async function v(){try{i("\u0110ang t\u1EA1m d\u1EEBng bot...","info");const t=await m();console.log("Bot paused:",t),a.botRunning=!1,a.botPaused=!0,u(),i("Bot \u0111\xE3 \u0111\u01B0\u1EE3c t\u1EA1m d\u1EEBng!","warning")}catch(t){console.error("Failed to pause bot:",t),i("L\u1ED7i khi t\u1EA1m d\u1EEBng bot: "+t.message,"error")}}async function x(){try{i("\u0110ang d\u1EEBng bot...","info");const t=await m();console.log("Bot stopped:",t),a.botRunning=!1,a.botPaused=!1,u(),i("Bot \u0111\xE3 \u0111\u01B0\u1EE3c d\u1EEBng!","success")}catch(t){console.error("Failed to stop bot:",t),i("L\u1ED7i khi d\u1EEBng bot: "+t.message,"error")}}async function h(){try{i("\u0110ang t\xECm ki\u1EBFm game...","info");const t=await $();console.log("Game detection result:",t),a.gameDetected=t.found,at(t),t.found?i("\u0110\xE3 t\xECm th\u1EA5y game Cabal!","success"):i("Kh\xF4ng t\xECm th\u1EA5y game Cabal. H\xE3y \u0111\u1EA3m b\u1EA3o game \u0111ang ch\u1EA1y.","warning")}catch(t){console.error("Failed to detect game:",t),i("L\u1ED7i khi t\xECm ki\u1EBFm game: "+t.message,"error")}}function u(){const t=document.getElementById("startBtn"),e=document.getElementById("pauseBtn"),n=document.getElementById("stopBtn");t&&e&&n&&(a.botRunning?(t.disabled=!0,t.classList.add("opacity-50"),e.disabled=!1,e.classList.remove("opacity-50"),n.disabled=!1,n.classList.remove("opacity-50")):(t.disabled=!1,t.classList.remove("opacity-50"),e.disabled=!0,e.classList.add("opacity-50"),n.disabled=!0,n.classList.add("opacity-50")))}function at(t){const e=document.getElementById("gameStatus"),n=document.getElementById("gameDetectionStatus");if(t&&t.found){const s="\u0110\xE3 t\xECm th\u1EA5y game",o="text-green-400";e&&(e.textContent=s,e.className=`text-xs text-center col-span-3 ${o}`),n&&(n.textContent=s,n.className=`text-xs text-center ${o}`)}else{const s="Kh\xF4ng t\xECm th\u1EA5y game",o="text-red-400";e&&(e.textContent=s,e.className=`text-xs text-center col-span-3 ${o}`),n&&(n.textContent=s,n.className=`text-xs text-center ${o}`)}}async function w(){try{console.log("Loading config...");const t=await P();a.config=t,console.log("Config loaded:",t),c()}catch(t){console.error("Failed to load config:",t),a.config={actions:[],timerInterval:1,lockKey:"Z",spaceSpamEnabled:!1,spaceSpamInterval:200,overlayEnabled:!1},c()}}async function k(){try{const t=await F();console.log("Config saved:",t),i("C\u1EA5u h\xECnh \u0111\xE3 \u0111\u01B0\u1EE3c l\u01B0u!","success")}catch(t){console.error("Failed to save config:",t),i("L\u1ED7i khi l\u01B0u c\u1EA5u h\xECnh!","error")}}function C(){w()}function S(t,e){a.config||(a.config={actions:[]}),a.config.actions[t]||(a.config.actions[t]={key:"",duration:1,enabled:!1}),a.config.actions[t].enabled=e,console.log(`Action ${t} toggled:`,e)}function g(t){!t||(t.removeEventListener("input",p),t.removeEventListener("keydown",b),t.addEventListener("input",p),t.addEventListener("keydown",b))}function p(t){const e=t.target,n=e.value;n.length>1&&(e.value=n.slice(-1))}function b(t){const e=t.target;[8,9,27,13,46].indexOf(t.keyCode)!==-1||t.keyCode===65&&t.ctrlKey===!0||t.keyCode===67&&t.ctrlKey===!0||t.keyCode===86&&t.ctrlKey===!0||t.keyCode===88&&t.ctrlKey===!0||e.value.length>=1&&t.key.length===1&&(e.value="")}function B(t){console.log("Editing action:",t),a.editingAction=t,a.currentTab="action",c(),r("action"),setTimeout(()=>{var o,l;const e=((l=(o=a.config)==null?void 0:o.actions)==null?void 0:l[t])||{key:"",duration:1},n=document.getElementById("editKey"),s=document.getElementById("editDuration");n&&(n.value=e.key||"",g(n)),s&&(s.value=e.duration||1),console.log("Edit form populated for action:",t)},100)}function E(){console.log("Canceling edit"),a.editingAction=-1,c(),a.currentTab&&r(a.currentTab)}async function L(){console.log("Saving action...");const t=document.getElementById("editKey"),e=document.getElementById("editDuration");if(!t||!e){console.error("Edit form inputs not found");return}const n=t.value.trim(),s=parseFloat(e.value)||1;if(!n){i("Vui l\xF2ng nh\u1EADp ph\xEDm!","error");return}try{console.log("Updating action:",a.editingAction,"with key:",n,"duration:",s);const o=await U(a.editingAction,n,s,!0);console.log("Action updated successfully:",o),a.config||(a.config={}),a.config.actions||(a.config.actions=[]),a.config.actions[a.editingAction]||(a.config.actions[a.editingAction]={}),a.config.actions[a.editingAction].key=n,a.config.actions[a.editingAction].duration=s,a.config.actions[a.editingAction].enabled=!0,a.editingAction=-1,c(),a.currentTab&&r(a.currentTab),i("H\xE0nh \u0111\u1ED9ng \u0111\xE3 \u0111\u01B0\u1EE3c l\u01B0u!","success")}catch(o){console.error("Failed to update action:",o),i("L\u1ED7i khi c\u1EADp nh\u1EADt h\xE0nh \u0111\u1ED9ng: "+o.message,"error")}}function T(t){const e=document.getElementById("intervalDisplay");e&&(e.textContent=`${t} gi\xE2y`),a.config&&(a.config.timerInterval=parseFloat(t))}async function I(t){try{const e=document.getElementById("spaceIntervalDisplay");e&&(e.textContent=t),a.config&&(a.config.spaceSpamInterval=parseInt(t)),await M(parseInt(t)),console.log("Space spam interval updated to:",t)}catch(e){console.error("Failed to update space spam interval:",e),i("L\u1ED7i khi c\u1EADp nh\u1EADt kho\u1EA3ng th\u1EDDi gian space: "+e.message,"error")}}async function A(t){try{console.log("Space spam toggled:",t),a.config&&(a.config.spaceSpamEnabled=t),await q(t),t?i("Spam nh\u1EB7t \u0111\u1ED3 \u0111\xE3 \u0111\u01B0\u1EE3c b\u1EADt","success"):i("Spam nh\u1EB7t \u0111\u1ED3 \u0111\xE3 \u0111\u01B0\u1EE3c t\u1EAFt","info")}catch(e){console.error("Failed to toggle space spam:",e),i("L\u1ED7i khi thay \u0111\u1ED5i spam nh\u1EB7t \u0111\u1ED3: "+e.message,"error");const n=document.getElementById("spaceSpamEnabled");n&&(n.checked=!t),a.config&&(a.config.spaceSpamEnabled=!t)}}function D(t){a.config&&(a.config.overlayEnabled=t),console.log("Overlay toggled:",t)}function st(){i("Nh\u1EA5n ph\xEDm b\u1EA1n mu\u1ED1n s\u1EED d\u1EE5ng l\xE0m ph\xEDm kh\xF3a...","info")}async function N(){try{const t=document.getElementById("windowStatus");t&&(t.textContent="\u0110ang \u0111\u1EB7t game lu\xF4n \u1EDF tr\xEAn...",t.className="text-xs text-yellow-400");const e=await R();console.log("Set always on top result:",e),t&&(t.textContent="Game \u0111\xE3 \u0111\u01B0\u1EE3c \u0111\u1EB7t lu\xF4n \u1EDF tr\xEAn!",t.className="text-xs text-green-400"),i("Game \u0111\xE3 \u0111\u01B0\u1EE3c \u0111\u1EB7t lu\xF4n \u1EDF tr\xEAn","success")}catch(t){console.error("Failed to set always on top:",t);const e=document.getElementById("windowStatus");e&&(e.textContent="L\u1ED7i: Kh\xF4ng th\u1EC3 \u0111\u1EB7t game lu\xF4n \u1EDF tr\xEAn",e.className="text-xs text-red-400"),i("L\u1ED7i khi \u0111\u1EB7t game lu\xF4n \u1EDF tr\xEAn: "+t.message,"error")}}async function K(){try{const t=document.getElementById("windowStatus");t&&(t.textContent="\u0110ang b\u1ECF t\xEDnh n\u0103ng lu\xF4n \u1EDF tr\xEAn...",t.className="text-xs text-yellow-400");const e=await H();console.log("Remove always on top result:",e),t&&(t.textContent="\u0110\xE3 b\u1ECF t\xEDnh n\u0103ng lu\xF4n \u1EDF tr\xEAn!",t.className="text-xs text-green-400"),i("\u0110\xE3 b\u1ECF t\xEDnh n\u0103ng lu\xF4n \u1EDF tr\xEAn","success")}catch(t){console.error("Failed to remove always on top:",t);const e=document.getElementById("windowStatus");e&&(e.textContent="L\u1ED7i: Kh\xF4ng th\u1EC3 b\u1ECF t\xEDnh n\u0103ng lu\xF4n \u1EDF tr\xEAn",e.className="text-xs text-red-400"),i("L\u1ED7i khi b\u1ECF t\xEDnh n\u0103ng lu\xF4n \u1EDF tr\xEAn: "+t.message,"error")}}async function O(){try{const t=document.getElementById("installStatus");t&&(t.textContent="\u0110ang c\xE0i \u0111\u1EB7t driver...",t.className="text-xs text-yellow-400");const e=await G();console.log("Driver installation result:",e),t&&(t.textContent="C\xE0i \u0111\u1EB7t th\xE0nh c\xF4ng! C\u1EA7n kh\u1EDFi \u0111\u1ED9ng l\u1EA1i h\u1EC7 th\u1ED1ng.",t.className="text-xs text-green-400")}catch(t){console.error("Failed to install driver:",t);const e=document.getElementById("installStatus");e&&(e.textContent="C\xE0i \u0111\u1EB7t th\u1EA5t b\u1EA1i! Vui l\xF2ng ch\u1EA1y v\u1EDBi quy\u1EC1n qu\u1EA3n tr\u1ECB.",e.className="text-xs text-red-400")}}window.startBot=y;window.pauseBot=v;window.stopBot=x;window.toggleAction=S;window.editAction=B;window.cancelEdit=E;window.saveAction=L;window.updateInterval=T;window.updateSpaceInterval=I;window.toggleSpaceSpam=A;window.toggleOverlay=D;window.recordLockKey=st;window.detectGame=h;window.saveConfiguration=k;window.loadConfiguration=C;window.installInterceptionDriver=O;window.setCabalAlwaysOnTop=N;window.removeCabalAlwaysOnTop=K;

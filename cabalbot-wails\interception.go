package main

import (
	"archive/zip"
	"crypto/tls"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// NewInterceptionDLL creates a new wrapper for the Interception DLL
func NewInterceptionDLL(dllPath string) (*InterceptionDLL, error) {
	dll := &InterceptionDLL{
		keyboardDeviceID: INTERCEPTION_KEYBOARD + 1, // First keyboard
	}

	// Load the Interception DLL
	var err error
	dll.dll, err = syscall.LoadDLL(dllPath)
	if err != nil {
		return nil, fmt.Errorf("Failed to load Interception DLL: %v", err)
	}

	// Get function pointers
	dll.createContext, err = dll.dll.FindProc("interception_create_context")
	if err != nil {
		return nil, fmt.Errorf("Failed to find interception_create_context: %v", err)
	}

	dll.destroyContext, err = dll.dll.FindProc("interception_destroy_context")
	if err != nil {
		return nil, fmt.Errorf("Failed to find interception_destroy_context: %v", err)
	}

	dll.send, err = dll.dll.FindProc("interception_send")
	if err != nil {
		return nil, fmt.Errorf("Failed to find interception_send: %v", err)
	}

	// Create the Interception context
	ret, _, err := dll.createContext.Call()
	if ret == 0 {
		return nil, fmt.Errorf("Failed to create Interception context: %v", err)
	}
	dll.context = ret
	dll.initialized = true

	log.Println("Interception driver initialized successfully")
	return dll, nil
}

// Cleanup releases the Interception context
func (dll *InterceptionDLL) Cleanup() {
	if dll.initialized && dll.context != 0 {
		dll.destroyContext.Call(dll.context)
		dll.context = 0
		dll.initialized = false
	}
	if dll.dll != nil {
		dll.dll.Release()
		dll.dll = nil
	}
}

// SendKeyboardStroke sends a keyboard stroke through the Interception driver
func (dll *InterceptionDLL) SendKeyboardStroke(scanCode uint16, keyDown bool) bool {
	if !dll.initialized || dll.context == 0 {
		return false
	}

	var state uint16 = 0
	if !keyDown {
		state = 1 // 0 = key down, 1 = key up
	}

	stroke := InterceptionKeyStroke{
		Code:        scanCode,
		State:       state,
		Information: 0,
	}

	ret, _, _ := dll.send.Call(
		dll.context,
		uintptr(dll.keyboardDeviceID),
		uintptr(unsafe.Pointer(&stroke)),
		1,
	)

	return ret == 1
}

// PressKey presses and releases a key with the specified duration
func (dll *InterceptionDLL) PressKey(scanCode uint16, duration float64) bool {
	if !dll.initialized || dll.context == 0 {
		return false
	}

	// Press key down
	if !dll.SendKeyboardStroke(scanCode, true) {
		return false
	}

	// Wait for the specified duration
	time.Sleep(time.Duration(duration * float64(time.Second)))

	// Release key
	return dll.SendKeyboardStroke(scanCode, false)
}

// InstallInterceptionDriver downloads and installs the Interception driver
func InstallInterceptionDriver() error {
	// Determine if we're running with admin privileges
	isAdmin := false
	if runtime.GOOS == "windows" {
		// Method to check admin on Windows
		_, err := os.Open("\\\\.\\PHYSICALDRIVE0")
		isAdmin = err == nil
	}

	if !isAdmin {
		state.installStatus = translate("Administrator privileges required. Please run as administrator.")
		return fmt.Errorf("administrator privileges required")
	}

	// Create temp directory for downloads
	tempDir, err := ioutil.TempDir("", "interception_install")
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Failed to create temp directory"), err)
		return fmt.Errorf("failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Download installer
	zipPath := filepath.Join(tempDir, "Interception.zip")

	// URL for the interception zip
	zipURL := "https://github.com/oblitum/Interception/releases/download/v1.0.1/Interception.zip"

	// Download the ZIP file
	state.installStatus = translate("Downloading Interception driver...")
	err = downloadFile(zipPath, zipURL)
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Failed to download Interception"), err)
		return fmt.Errorf("failed to download Interception: %v", err)
	}

	// Extract the ZIP file
	state.installStatus = translate("Extracting Interception files...")
	err = unzipFile(zipPath, tempDir)
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Failed to extract Interception"), err)
		return fmt.Errorf("failed to extract Interception: %v", err)
	}

	// Now search for the installer and DLL within subdirectories
	var installerPath string
	var dllPath string
	var dllFound bool

	state.installStatus = translate("Locating installer and DLL files...")

	// Walk through the extracted files to find the installer and DLL
	err = filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Look for the installer
		if info.Name() == "install-interception.exe" {
			installerPath = path
			log.Printf("Found installer at: %s\n", installerPath)
		}

		// Look for the dll (prefer x64 if available)
		if info.Name() == "interception.dll" {
			// If x64 is in the path, prefer it
			if strings.Contains(strings.ToLower(path), "x64") {
				dllPath = path
				dllFound = true
				log.Printf("Found x64 DLL at: %s\n", dllPath)
			} else if !dllFound {
				// Otherwise use any DLL found
				dllPath = path
				log.Printf("Found DLL at: %s\n", dllPath)
			}
		}

		return nil
	})

	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Error searching files"), err)
		return fmt.Errorf("error searching files: %v", err)
	}

	// Check if files were found
	if installerPath == "" {
		state.installStatus = translate("Installer not found in extracted files")
		return fmt.Errorf("installer not found in extracted files")
	}

	if dllPath == "" {
		state.installStatus = translate("DLL not found in extracted files")
		return fmt.Errorf("DLL not found in extracted files")
	}

	// Copy DLL to current directory
	currentDir, _ := os.Getwd()
	localDllPath := filepath.Join(currentDir, "interception.dll")

	// Read and write the DLL file
	data, err := ioutil.ReadFile(dllPath)
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Failed to read DLL"), err)
		return fmt.Errorf("failed to read DLL: %v", err)
	}

	err = ioutil.WriteFile(localDllPath, data, 0644)
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v", translate("Failed to save DLL locally"), err)
		return fmt.Errorf("failed to save DLL locally: %v", err)
	}

	// Run the installer
	state.installStatus = translate("Running Interception installer...")
	cmd := exec.Command(installerPath, "/install")
	output, err := cmd.CombinedOutput()
	if err != nil {
		state.installStatus = fmt.Sprintf("%s: %v\n%s: %s",
			translate("Failed to execute installer"), err,
			translate("Output"), output)
		return fmt.Errorf("failed to execute installer: %v\nOutput: %s", err, output)
	}

	state.installStatus = translate("Installation successful! A system reboot is required.")
	state.driverStatus = translate("✓ Driver installed (reboot required)")
	return nil
}

// downloadFile downloads a file from the specified URL with multiple fallback methods
func downloadFile(filepath, url string) error {
	// Check if file already exists and try to remove it first
	if _, err := os.Stat(filepath); err == nil {
		// File exists, try to remove it
		if err := os.Remove(filepath); err != nil {
			// If removal fails, use a different filename
			filepath = filepath + fmt.Sprintf(".%d", time.Now().UnixNano())
		}
	}

	// Create the file with exclusive access
	out, err := os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}

	// Ensure file is closed when function returns
	defer func() {
		out.Close()
		// If download failed and file exists but is empty, clean it up
		if fi, statErr := os.Stat(filepath); statErr == nil && fi.Size() == 0 {
			os.Remove(filepath)
		}
	}()

	// First attempt: Try to use curl if available (more reliable than PowerShell)
	var curlCmd *exec.Cmd
	if runtime.GOOS == "windows" {
		_, err := exec.LookPath("curl")
		if err == nil {
			curlCmd = exec.Command("curl", "-L", "-o", filepath, url)
		}
	} else {
		curlCmd = exec.Command("curl", "-L", "-o", filepath, url)
	}

	if curlCmd != nil {
		// Close the file before letting curl handle it
		out.Close()

		output, err := curlCmd.CombinedOutput()
		if err == nil {
			log.Println("Download successful using curl")
			return nil
		}
		log.Printf("Curl download failed: %v\nOutput: %s\n", err, output)
		log.Println("Trying PowerShell method...")

		// Reopen the file since curl failed
		out, err = os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return fmt.Errorf("failed to reopen file: %v", err)
		}
	}

	// Second attempt: Use PowerShell with improved settings
	if runtime.GOOS == "windows" {
		// Close file before PowerShell tries to access it
		out.Close()

		// Generate a unique temp filename for PowerShell
		psTempFile := filepath + ".ps.tmp"

		powershellCmd := `
			try {
				[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12;
				$wc = New-Object System.Net.WebClient;
				$wc.Proxy = [System.Net.WebRequest]::GetSystemWebProxy();
				$wc.Proxy.Credentials = [System.Net.CredentialCache]::DefaultNetworkCredentials;
				$wc.DownloadFile('%s', '%s');
				Write-Output "Download successful using PowerShell WebClient";
			} catch {
				Write-Error $_.Exception.ToString();
				exit 1;
			}
		`
		cmd := exec.Command("powershell", "-Command", fmt.Sprintf(powershellCmd, url, psTempFile))
		output, err := cmd.CombinedOutput()
		if err == nil {
			log.Println(string(output))

			// Move the temp file to the target file
			if err := os.Rename(psTempFile, filepath); err != nil {
				// If move fails, try to copy contents
				data, readErr := ioutil.ReadFile(psTempFile)
				if readErr == nil {
					if writeErr := ioutil.WriteFile(filepath, data, 0644); writeErr == nil {
						os.Remove(psTempFile)
						return nil
					}
				}
				return fmt.Errorf("PowerShell download succeeded but couldn't move file: %v", err)
			}
			return nil
		}
		log.Printf("PowerShell download failed: %v\nOutput: %s\n", err, output)
		log.Println("Trying Go HTTP client method...")

		// Reopen the file since PowerShell failed
		out, err = os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
		if err != nil {
			return fmt.Errorf("failed to reopen file: %v", err)
		}
	}

	// Third attempt: Use native Go HTTP client
	log.Println("Attempting download with Go HTTP client...")

	// Reset file for the new attempt
	if err := out.Truncate(0); err != nil {
		return fmt.Errorf("failed to truncate file: %v", err)
	}
	if _, err := out.Seek(0, 0); err != nil {
		return fmt.Errorf("failed to seek to beginning of file: %v", err)
	}

	// Create HTTP client with timeout and TLS settings
	client := &http.Client{
		Timeout: 60 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				MinVersion: tls.VersionTLS12,
			},
		},
	}

	// Create a new request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %v", err)
	}
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)")

	// Perform the request
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP request failed: %v", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request returned status: %s", resp.Status)
	}

	// Copy the response body to the file
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("failed to write downloaded content: %v", err)
	}

	log.Println("Download successful using Go HTTP client")
	return nil
}

// unzipFile extracts a ZIP file to the specified destination
func unzipFile(zipPath, destPath string) error {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer reader.Close()

	for _, file := range reader.File {
		filePath := filepath.Join(destPath, file.Name)

		if file.FileInfo().IsDir() {
			os.MkdirAll(filePath, os.ModePerm)
			continue
		}

		if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {
			return err
		}

		outFile, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			return err
		}

		rc, err := file.Open()
		if err != nil {
			outFile.Close()
			return err
		}

		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()

		if err != nil {
			return err
		}
	}

	return nil
}

// IsInterceptionDriverInstalled checks if the Interception driver is installed
func IsInterceptionDriverInstalled() bool {
	// Check if the Interception DLL exists
	_, err := os.Stat("interception.dll")
	if err != nil {
		return false
	}

	// Try to load the DLL
	dll, err := syscall.LoadDLL("interception.dll")
	if err != nil {
		return false
	}
	defer dll.Release()

	// Try to find a function in the DLL
	_, err = dll.FindProc("interception_create_context")
	return err == nil
}

// installDriver installs the Interception driver
func installDriver() {
	go func() {
		state.installStatus = translate("Starting installation...")
		err := InstallInterceptionDriver()
		if err != nil {
			state.installStatus = fmt.Sprintf("%s: %v\n%s",
				translate("Installation failed"), err,
				translate("Please run this program as administrator and try again."))
		} else {
			state.installStatus = translate("Installation successful! A system reboot is required.")
		}
	}()
}

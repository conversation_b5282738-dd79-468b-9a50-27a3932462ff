package main

import (
	"fmt"
	"log"
	"os"
	"runtime"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

// RunBot runs the bot with the given configuration
func RunBot(config *BotConfig, stop<PERSON>han chan struct{}) {
	// Try to initialize the Interception driver
	dllPath := "interception.dll"

	// Check if the DLL exists
	_, err := os.Stat(dllPath)
	if os.IsNotExist(err) {
		state.statusMessage = translate("Error: interception.dll not found. Please install the Interception driver.")
		return
	}

	// Initialize the Interception driver
	dll, err := NewInterceptionDLL(dllPath)
	if err != nil {
		state.statusMessage = fmt.Sprintf("%s: %v", translate("Error initializing Interception driver"), err)
		return
	}
	defer dll.Cleanup()

	state.statusMessage = translate("Bot started")

	// Main bot loop
	for {
		select {
		case <-stopChan:
			// Bot was stopped
			state.statusMessage = translate("Bot stopped")
			return
		default:
			// Continue running
		}

		// Get a copy of the configuration
		config.Mutex.Lock()
		actions := make([]BotAction, len(config.Actions))
		copy(actions, config.Actions)
		interval := config.TimerInterval
		lockScanCode := config.LockScanCode
		lockKey := config.LockKey
		running := config.Running
		config.Mutex.Unlock()

		if !running {
			return
		}

		// Run through all enabled actions in sequence
		for i, action := range actions {
			if !action.Enabled {
				continue
			}

			// Get the latest running state
			config.Mutex.Lock()
			running = config.Running
			config.Mutex.Unlock()

			if !running {
				return
			}

			// Execute the action
			state.statusMessage = fmt.Sprintf("%s: %s %s %.2f %s",
				translate("Executing action"), action.Key,
				translate("for"), action.Duration,
				translate("seconds"))
			dll.PressKey(action.ScanCode, action.Duration)

			// After each action, spam the lock key a few times for lock-on
			if i < len(actions)-1 {
				state.statusMessage = fmt.Sprintf("%s %s",
					translate("Spamming lock-on key"), lockKey)
				for j := 0; j < 3; j++ {
					dll.PressKey(lockScanCode, 0.1)
					time.Sleep(100 * time.Millisecond)
				}
			}

			// Wait for the interval time between actions
			if i < len(actions)-1 {
				state.statusMessage = fmt.Sprintf("%s %.1f %s %s",
					translate("Waiting"), interval,
					translate("seconds"),
					translate("before next action"))

				// Check if we should stop or if configuration was updated
				select {
				case <-stopChan:
					state.statusMessage = translate("Bot stopped during interval")
					return
				case <-time.After(time.Duration(interval * float64(time.Second))):
					// Continue to next action
				}
			}
		}
	}
}

// RunSpaceSpam runs the space key spam for item collection
func RunSpaceSpam(config *BotConfig, stopChan chan struct{}) {
	// Try to initialize the Interception driver
	dllPath := "interception.dll"

	// Check if the DLL exists
	_, err := os.Stat(dllPath)
	if os.IsNotExist(err) {
		state.statusMessage = translate("Error: interception.dll not found. Please install the Interception driver.")
		return
	}

	// Initialize the Interception driver
	dll, err := NewInterceptionDLL(dllPath)
	if err != nil {
		state.statusMessage = fmt.Sprintf("%s: %v", translate("Error initializing Interception driver"), err)
		return
	}
	defer dll.Cleanup()

	// Update status message to show space spam is active
	state.statusMessage = translate("Thu thập vật phẩm đã bắt đầu")

	// Counter for items collected (for visual feedback)
	itemsCollected := 0

	// Main space spam loop
	for {
		select {
		case <-stopChan:
			// Space spam was stopped
			state.statusMessage = translate("Thu thập vật phẩm đã dừng")
			return
		default:
			// Continue running
		}

		// Get the latest configuration
		config.Mutex.Lock()
		spaceSpamEnabled := config.SpaceSpamEnabled
		spaceSpamInterval := config.SpaceSpamInterval
		config.Mutex.Unlock()

		if !spaceSpamEnabled {
			state.statusMessage = translate("Thu thập vật phẩm đã dừng")
			return
		}

		// Press SPACE key
		dll.PressKey(SCANCODE_SPACE, 0.05)

		// Increment counter and update status every 10 presses
		itemsCollected++
		if itemsCollected%10 == 0 {
			state.statusMessage = fmt.Sprintf("%s - %d %s",
				translate("Thu thập vật phẩm đang hoạt động"),
				itemsCollected,
				translate("lần nhấn"))
		}

		// Wait for the specified interval
		time.Sleep(time.Duration(spaceSpamInterval) * time.Millisecond)
	}
}

// FindCabalProcess tries to find the CabalMain.exe process using Windows API
func FindCabalProcess() (GameProcessInfo, error) {
	result := GameProcessInfo{
		Found: false,
	}

	// This function is Windows-specific
	if runtime.GOOS != "windows" {
		return result, fmt.Errorf("unsupported OS: %s", runtime.GOOS)
	}

	// Import necessary Windows API functions
	kernel32 := syscall.NewLazyDLL("kernel32.dll")
	psapi := syscall.NewLazyDLL("psapi.dll")

	createToolhelp32Snapshot := kernel32.NewProc("CreateToolhelp32Snapshot")
	process32First := kernel32.NewProc("Process32FirstW")
	process32Next := kernel32.NewProc("Process32NextW")
	closeHandle := kernel32.NewProc("CloseHandle")
	openProcess := kernel32.NewProc("OpenProcess")
	getModuleFileNameEx := psapi.NewProc("GetModuleFileNameExW")

	// Constants for Windows API
	const (
		TH32CS_SNAPPROCESS        = 0x00000002
		PROCESS_QUERY_INFORMATION = 0x0400
		PROCESS_VM_READ           = 0x0010
		MAX_PATH                  = 260
	)

	// Create a snapshot of all processes
	snapshot, _, _ := createToolhelp32Snapshot.Call(uintptr(TH32CS_SNAPPROCESS), 0)
	if snapshot == uintptr(syscall.InvalidHandle) {
		return result, fmt.Errorf("failed to create process snapshot")
	}
	defer closeHandle.Call(snapshot)

	// Define the process entry structure
	type PROCESSENTRY32 struct {
		dwSize              uint32
		cntUsage            uint32
		th32ProcessID       uint32
		th32DefaultHeapID   uintptr
		th32ModuleID        uint32
		cntThreads          uint32
		th32ParentProcessID uint32
		pcPriClassBase      int32
		dwFlags             uint32
		szExeFile           [MAX_PATH]uint16
	}

	// Initialize the process entry
	var entry PROCESSENTRY32
	entry.dwSize = uint32(unsafe.Sizeof(entry))

	// Get the first process
	ret, _, _ := process32First.Call(snapshot, uintptr(unsafe.Pointer(&entry)))
	if ret == 0 {
		return result, fmt.Errorf("failed to get first process")
	}

	// Iterate through all processes
	for {
		// Convert the process name from UTF16 to string
		processName := syscall.UTF16ToString(entry.szExeFile[:])

		// Check if this is the Cabal process
		if strings.EqualFold(processName, "CabalMain.exe") {
			// Found the process, now get its full path
			processHandle, _, _ := openProcess.Call(
				uintptr(PROCESS_QUERY_INFORMATION|PROCESS_VM_READ),
				0,
				uintptr(entry.th32ProcessID),
			)

			if processHandle != 0 && processHandle != uintptr(syscall.InvalidHandle) {
				defer closeHandle.Call(processHandle)

				// Get the full path of the executable
				var pathBuf [MAX_PATH]uint16
				ret, _, _ := getModuleFileNameEx.Call(
					processHandle,
					0,
					uintptr(unsafe.Pointer(&pathBuf[0])),
					uintptr(MAX_PATH),
				)

				if ret > 0 {
					exePath := syscall.UTF16ToString(pathBuf[:])
					result.ProcessID = entry.th32ProcessID
					result.Path = exePath
					result.Found = true
					return result, nil
				}
			}

			// If we couldn't get the path, at least return the PID
			result.ProcessID = entry.th32ProcessID
			result.Found = true

			// Try to guess the path based on common installation locations
			commonPaths := []string{
				"C:\\Program Files\\Cabal Online\\CabalMain.exe",
				"C:\\Program Files (x86)\\Cabal Online\\CabalMain.exe",
				"D:\\Program Files\\Cabal Online\\CabalMain.exe",
				"D:\\Program Files (x86)\\Cabal Online\\CabalMain.exe",
			}

			for _, path := range commonPaths {
				if _, err := os.Stat(path); err == nil {
					result.Path = path
					break
				}
			}

			return result, nil
		}

		// Get the next process
		ret, _, _ = process32Next.Call(snapshot, uintptr(unsafe.Pointer(&entry)))
		if ret == 0 {
			break
		}
	}

	return result, fmt.Errorf("CabalMain.exe not found")
}

// FindCabalWindow finds the main window of the Cabal game
func FindCabalWindow() (syscall.Handle, error) {
	// Import necessary Windows API functions
	user32 := syscall.NewLazyDLL("user32.dll")
	enumWindows := user32.NewProc("EnumWindows")
	getWindowTextW := user32.NewProc("GetWindowTextW")
	getWindowThreadProcessId := user32.NewProc("GetWindowThreadProcessId")
	isWindowVisible := user32.NewProc("IsWindowVisible")

	var foundWindow syscall.Handle
	var targetPID uint32

	// First, get the Cabal process ID
	process, err := FindCabalProcess()
	if err != nil {
		return 0, fmt.Errorf("Cabal process not found: %v", err)
	}
	targetPID = process.ProcessID

	// Callback function for EnumWindows
	callback := syscall.NewCallback(func(hwnd syscall.Handle, lParam uintptr) uintptr {
		// Check if window is visible
		visible, _, _ := isWindowVisible.Call(uintptr(hwnd))
		if visible == 0 {
			return 1 // Continue enumeration
		}

		// Get the process ID of this window
		var processID uint32
		getWindowThreadProcessId.Call(uintptr(hwnd), uintptr(unsafe.Pointer(&processID)))

		// Check if this window belongs to our target process
		if processID == targetPID {
			// Get window title
			var titleBuf [256]uint16
			getWindowTextW.Call(uintptr(hwnd), uintptr(unsafe.Pointer(&titleBuf[0])), 256)
			title := syscall.UTF16ToString(titleBuf[:])

			// Look for Cabal-related window titles
			if strings.Contains(strings.ToLower(title), "cabal") ||
				strings.Contains(strings.ToLower(title), "game") ||
				len(title) > 0 { // Any window with a title from the Cabal process
				foundWindow = hwnd
				return 0 // Stop enumeration
			}
		}

		return 1 // Continue enumeration
	})

	// Enumerate all windows
	enumWindows.Call(callback, 0)

	if foundWindow == 0 {
		return 0, fmt.Errorf("Cabal game window not found")
	}

	return foundWindow, nil
}

// SetCabalWindowAlwaysOnTop sets the Cabal game window to always stay on top and brings it to focus
func SetCabalWindowAlwaysOnTop() error {
	// Import necessary Windows API functions
	user32 := syscall.NewLazyDLL("user32.dll")
	setWindowPos := user32.NewProc("SetWindowPos")
	setForegroundWindow := user32.NewProc("SetForegroundWindow")
	showWindow := user32.NewProc("ShowWindow")
	bringWindowToTop := user32.NewProc("BringWindowToTop")

	// Constants for SetWindowPos
	const (
		HWND_TOPMOST = ^uintptr(0) // -1
		SWP_NOMOVE   = 0x0002
		SWP_NOSIZE   = 0x0001
		SW_RESTORE   = 9
		SW_SHOW      = 5
	)

	// Find the Cabal window
	hwnd, err := FindCabalWindow()
	if err != nil {
		return fmt.Errorf("failed to find Cabal window: %v", err)
	}

	// Restore the window if it's minimized
	showWindow.Call(uintptr(hwnd), SW_RESTORE)

	// Set the window to always on top
	ret, _, _ := setWindowPos.Call(
		uintptr(hwnd),
		HWND_TOPMOST,
		0, 0, 0, 0,
		SWP_NOMOVE|SWP_NOSIZE,
	)

	if ret == 0 {
		return fmt.Errorf("failed to set window always on top")
	}

	// Bring the window to the foreground
	setForegroundWindow.Call(uintptr(hwnd))
	bringWindowToTop.Call(uintptr(hwnd))

	log.Println("Cabal window set to always on top and brought to focus")
	return nil
}

// RemoveCabalWindowAlwaysOnTop removes the always on top setting from the Cabal game window
func RemoveCabalWindowAlwaysOnTop() error {
	// Import necessary Windows API functions
	user32 := syscall.NewLazyDLL("user32.dll")
	setWindowPos := user32.NewProc("SetWindowPos")

	// Constants for SetWindowPos
	const (
		HWND_NOTOPMOST = ^uintptr(1) // -2
		SWP_NOMOVE     = 0x0002
		SWP_NOSIZE     = 0x0001
	)

	// Find the Cabal window
	hwnd, err := FindCabalWindow()
	if err != nil {
		return fmt.Errorf("failed to find Cabal window: %v", err)
	}

	// Remove the always on top setting
	ret, _, _ := setWindowPos.Call(
		uintptr(hwnd),
		HWND_NOTOPMOST,
		0, 0, 0, 0,
		SWP_NOMOVE|SWP_NOSIZE,
	)

	if ret == 0 {
		return fmt.Errorf("failed to remove always on top setting")
	}

	log.Println("Cabal window always on top setting removed")
	return nil
}

// startBot starts the bot
func startBot() {
	state.config.Mutex.Lock()
	if state.config.Running {
		state.statusMessage = translate("Bot is already running!")
		state.config.Mutex.Unlock()
		return
	}
	state.config.Running = true
	state.config.StopChan = make(chan struct{})
	stopChan := state.config.StopChan
	state.config.Mutex.Unlock()

	state.statusMessage = translate("Bot started! Click Stop to stop the bot.")

	// Set Cabal window to always on top when bot starts
	go func() {
		err := SetCabalWindowAlwaysOnTop()
		if err != nil {
			log.Printf("Failed to set Cabal window always on top: %v", err)
		}
	}()

	// Start the bot in a goroutine
	go RunBot(state.config, stopChan)

	// Start space key spam if enabled
	state.config.Mutex.Lock()
	spaceSpamEnabled := state.config.SpaceSpamEnabled
	if spaceSpamEnabled {
		state.config.SpaceStopChan = make(chan struct{})
		spaceStopChan := state.config.SpaceStopChan
		state.config.Mutex.Unlock()
		go RunSpaceSpam(state.config, spaceStopChan)
	} else {
		state.config.Mutex.Unlock()
	}
}

// stopBot stops the bot
func stopBot() {
	state.config.Mutex.Lock()
	if state.config.Running {
		state.config.Running = false
		close(state.config.StopChan)
	}

	// Stop space key spam if running
	if state.config.SpaceSpamEnabled && state.config.SpaceStopChan != nil {
		close(state.config.SpaceStopChan)
		state.config.SpaceStopChan = nil
	}

	state.config.Mutex.Unlock()
	state.statusMessage = translate("Bot stopped")
}

// toggleSpaceSpam enables or disables space key spam
func toggleSpaceSpam(enabled bool) {
	state.config.Mutex.Lock()
	state.config.SpaceSpamEnabled = enabled

	// If bot is running, start/stop space spam accordingly
	if state.config.Running {
		if enabled && state.config.SpaceStopChan == nil {
			// Start space spam
			state.config.SpaceStopChan = make(chan struct{})
			spaceStopChan := state.config.SpaceStopChan
			state.config.Mutex.Unlock()
			go RunSpaceSpam(state.config, spaceStopChan)
		} else if !enabled && state.config.SpaceStopChan != nil {
			// Stop space spam
			close(state.config.SpaceStopChan)
			state.config.SpaceStopChan = nil
			state.config.Mutex.Unlock()
		} else {
			state.config.Mutex.Unlock()
		}
	} else {
		state.config.Mutex.Unlock()
	}
}

// detectGameProcess attempts to detect the Cabal game process
func detectGameProcess() {
	go func() {
		process, err := FindCabalProcess()
		if err != nil {
			log.Printf("Failed to find game process: %v", err)
			state.gameProcess.Found = false
			state.statusMessage = translate("Game not detected")
		} else {
			state.gameProcess = process
			state.statusMessage = translate("Game detected")
		}
	}()
}

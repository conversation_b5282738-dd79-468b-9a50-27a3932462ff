/* Reset and base styles for the Wails app */
html, body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-sizing: border-box;
}

*, *::before, *::after {
    box-sizing: border-box;
}

#app {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
}

.app-container {
    width: 100%;
    height: 100%;
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Responsive design for different screen sizes */
@media (max-width: 768px) {
    .app-container {
        font-size: 14px;
    }

    .tab-btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
    }

    .grid-cols-3 {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        gap: 8px !important;
    }

    .control-btn {
        padding: 12px 8px !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .app-container {
        font-size: 15px;
    }
}

@media (min-width: 1025px) {
    .app-container {
        font-size: 16px;
    }
}

/* Flexible layout components */
.tab-content {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 200px);
}

.flex-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.flex-responsive > * {
    flex: 1;
    min-width: 120px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Custom range slider styles */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: #3B82F6;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border: 2px solid #1E40AF;
    cursor: pointer;
}

input[type="range"]::-moz-range-track {
    background: #374151;
    height: 8px;
    border-radius: 4px;
    border: none;
}

input[type="range"]::-moz-range-thumb {
    background: #3B82F6;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    border: 2px solid #1E40AF;
    cursor: pointer;
}

/* Button disabled state */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* Prevent space key from scrolling the page */
body {
    overflow: hidden; /* Already set above, but ensuring it's here */
}

/* Disable default space key behavior */
.no-space-scroll {
    overflow: hidden;
}

/* Ensure the app container doesn't scroll */
.app-container {
    overflow: hidden;
}

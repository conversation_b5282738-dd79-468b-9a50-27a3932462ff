package main

import (
	"image/color"
	"log"
	"os"

	g "github.com/AllenDang/giu"
)

// Global state
var state AppState

// Initialize logger to file instead of console
func initLogger() {
	logFile, err := os.OpenFile("cabal_bot.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		// If we can't open the log file, we'll continue without logging
		return
	}
	state.logFile = logFile

	// Redirect all standard output and error to the log file
	log.SetOutput(logFile)
	os.Stdout = logFile
	os.Stderr = logFile

	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	log.Println("Logger initialized")
}

// Initialization
func init() {
	// Initialize logger to file instead of console
	initLogger()

	// Create default config
	state.config = NewBotConfig()
	state.statusMessage = translate("Ready")
	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
	state.transparency = 0.8
	state.overlayPosition = "top-right"
	state.directInputEnabled = true
	state.overlayEnabled = false // Disable overlay by default

	// Initialize wizard state
	state.wizardState = WizardState{
		CurrentStep: 1,
		TotalSteps:  4,
		Completed:   false,
	}

	// Initialize key recording state
	state.keyRecordingState = KeyRecordingState{
		Recording:      false,
		TargetKey:      nil,
		TargetScanCode: nil,
	}

	// Initialize game process info
	state.gameProcess = GameProcessInfo{
		ProcessID: 0,
		Path:      "",
		Found:     false,
	}

	// Check if driver is installed
	if IsInterceptionDriverInstalled() {
		state.driverStatus = translate("✓ Driver installed")
	} else {
		state.driverStatus = translate("⚠ Driver not installed")
	}

	// Initialize keyboard hook
	initKeyboardHook()
}

// updateOverlaySettings updates the window properties for overlay mode
func updateOverlaySettings(window *g.MasterWindow) {
	if state.overlayEnabled {
		// For overlay mode, we need to set a transparent background color
		// We can do this by setting the clear color alpha
		// Convert transparency (0.0-1.0) to an RGBA color with alpha
		alpha := uint8(state.transparency * 255)
		bgColor := color.RGBA{R: 0, G: 0, B: 0, A: alpha}
		window.SetBgColor(bgColor)

		// We can't directly set window flags after creation in current giu API
		// But we can make the window have Frameless and Floating flags at creation time
		// This would require modifying the window creation code in main()
	} else {
		// Reset to normal window with opaque background
		bgColor := color.RGBA{R: 0, G: 0, B: 0, A: 255} // Fully opaque
		window.SetBgColor(bgColor)
	}
}

// processKeyboardInput processes keyboard input captured by the global hook
func processKeyboardInput(key string, scanCode uint16) {
	if state.keyRecordingState.Recording {
		// Check if we need to update a target key
		if state.keyRecordingState.TargetKey != nil {
			*state.keyRecordingState.TargetKey = key

			// If we also need to update the scan code
			if state.keyRecordingState.TargetScanCode != nil {
				*state.keyRecordingState.TargetScanCode = scanCode
			}

			// Stop recording and update UI
			stopRecordingKey()
			g.Update()
		}
	}
}

// loop is the main GUI loop
func loop() {
	// Display setup wizard if not completed
	if !state.wizardState.Completed {
		g.SingleWindow().Layout(
			renderWizardStep(),
		)
		return
	}

	// Use the new main layout that matches the HTML design
	g.SingleWindow().Layout(
		renderMainLayout(),
	)
}

func main() {
	// Create window with hidden console
	// When building, use: go build -ldflags="-H=windowsgui"

	// Initialize flags for normal window (not overlay by default)
	var flags g.MasterWindowFlags = 0

	// Only enable overlay if explicitly requested and configured
	if state.overlayEnabled && state.config != nil {
		flags |= g.MasterWindowFlagsFloating | g.MasterWindowFlagsFrameless
	}

	// Create a window matching the HTML design size with Vietnamese title
	wnd := g.NewMasterWindow("CABALBOT v2.0 - Trình Hỗ Trợ Tự Động", 600, 400, flags)

	// Set initial background color based on overlay settings
	if state.overlayEnabled {
		// Convert transparency (0.0-1.0) to uint8 alpha value (0-255)
		alpha := uint8(state.transparency * 255)
		wnd.SetBgColor(color.RGBA{R: 0, G: 0, B: 0, A: alpha})

		// Position the window based on overlay position setting
		// Note: We're using SetPos which is supported by the giu library
		switch state.overlayPosition {
		case "top-left":
			wnd.SetPos(0, 0)
		case "top-right":
			wnd.SetPos(1920-650, 0) // Assuming 1920x1080 resolution
		case "bottom-left":
			wnd.SetPos(0, 1080-450) // Assuming 1920x1080 resolution
		case "bottom-right":
			wnd.SetPos(1920-650, 1080-450) // Assuming 1920x1080 resolution
		case "center":
			wnd.SetPos(1920/2-325, 1080/2-225) // Assuming 1920x1080 resolution
		default:
			// Default to top-left
			wnd.SetPos(0, 0)
		}
	}

	// Run the main window loop
	wnd.Run(loop)

	// Cleanup when application closes
	cleanupKeyboardHook()

	if state.logFile != nil {
		state.logFile.Close()
	}
}

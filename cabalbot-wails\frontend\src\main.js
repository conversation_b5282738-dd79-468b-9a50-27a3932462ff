import './style.css';
import './app.css';

// Import Wails runtime and API functions
import {
    StartBot,
    StopBot,
    DetectGame,
    InstallDriver,
    SaveConfig,
    GetConfig,
    GetAppState,
    UpdateAction,
    GetWizardState,
    NextWizardStep,
    PreviousWizardStep,
    CompleteWizard,
    SetLockKey,
    SetTimerInterval,
    SetSpaceSpamInterval,
    ToggleSpaceSpam,
    ToggleOverlay,
    StartKeyRecording,
    StopKeyRecording,
    GetAvailableKeys,
    SetCabalAlwaysOnTop,
    RemoveCabalAlwaysOnTop
} from '../wailsjs/go/main/App';

// Global state
let appState = {
    config: null,
    gameDetected: false,
    botRunning: false,
    botPaused: false,
    currentTab: 'main',
    editingAction: -1
};

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notification if any
    const existing = document.querySelector('.notification');
    if (existing) {
        existing.remove();
    }

    const notification = document.createElement('div');
    notification.className = `notification fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 transition-all duration-300`;

    switch (type) {
        case 'success':
            notification.classList.add('bg-green-600');
            break;
        case 'error':
            notification.classList.add('bg-red-600');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-600');
            break;
        default:
            notification.classList.add('bg-blue-600');
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 3000);
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
    setupKeyboardEventHandlers();
});

async function initializeApp() {
    console.log('Initializing app...');
    renderApp();
    await loadConfig();
    updateDateTime();
    setInterval(updateDateTime, 60000);
    console.log('App initialized');
}

// Setup keyboard event handlers to prevent unwanted behavior
function setupKeyboardEventHandlers() {
    // Prevent space key from scrolling the page
    document.addEventListener('keydown', function(e) {
        // Prevent space key from scrolling when not in an input field
        if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            e.stopPropagation();
            console.log('Space key scroll prevented');
            return false;
        }

        // Prevent other keys that might interfere with the bot
        if (e.code === 'F5' || (e.ctrlKey && e.code === 'KeyR')) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Page refresh prevented');
            return false;
        }

        // Prevent arrow keys from scrolling
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code) &&
            e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, true); // Use capture phase

    // Also prevent space key on keyup to be thorough
    document.addEventListener('keyup', function(e) {
        if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, true); // Use capture phase

    // Prevent keypress events for space
    document.addEventListener('keypress', function(e) {
        if (e.code === 'Space' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, true); // Use capture phase

    // Prevent context menu on right click
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });

    // Prevent wheel scrolling
    document.addEventListener('wheel', function(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, { passive: false });

    console.log('Keyboard event handlers set up');
}

function renderApp() {
    console.log('Rendering app...');

    // Add no-space-scroll class to body to prevent space key scrolling
    document.body.classList.add('no-space-scroll');

    const appHtml = `
        <div class="app-container">
            <!-- Background Image -->
            <div class="absolute inset-0 z-0" style="background-image: url('https://i.ibb.co/67P9MrGT/auto-bgr.png'); background-size: cover; background-position: center;"></div>
            <div class="overlay"></div>

            <!-- Main Content -->
            <div class="relative z-10 h-full flex flex-col">
                <!-- Header with Logo -->
                <div class="flex items-center justify-between px-4 py-2 bg-black bg-opacity-30">
                    <div class="flex items-center">
                        <h1 class="text-white font-bold text-xl">CABAL<strong class="text-blue-400">BOT</strong> v2.0</h1>
                    </div>
                    <div class="text-xs text-gray-300">
                        <span id="datetime"></span>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="flex border-b border-gray-700 bg-black bg-opacity-40">
                    <button class="tab-btn active flex-1 py-3 px-4 text-white font-medium text-sm tracking-wider" data-tab="main">
                        <i class="fas fa-home mr-2"></i> Main
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="action">
                        <i class="fas fa-play-circle mr-2"></i> Action
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="config">
                        <i class="fas fa-cog mr-2"></i> Config
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="window">
                        <i class="fas fa-window-maximize mr-2"></i> Window
                    </button>
                    <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="driver">
                        <i class="fas fa-microchip mr-2"></i> Driver
                    </button>
                </div>

                <!-- Tab Contents -->
                <div class="flex-1 overflow-y-auto p-4 bg-black bg-opacity-30" id="tab-container">
                    <!-- Tabs will be rendered here -->
                </div>

                <!-- Status Bar -->
                <div class="bg-black bg-opacity-60 text-xs text-gray-400 px-4 py-2 flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <span>System Ready</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span><i class="fas fa-satellite-dish mr-1"></i> 87%</span>
                        <span><i class="fas fa-database mr-1"></i> 24%</span>
                        <span><i class="fas fa-plug mr-1"></i> 3/4</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.querySelector('#app').innerHTML = appHtml;

    // Render initial tab content
    renderTabContent();

    // Setup event listeners
    setupEventListeners();
    console.log('App rendered and event listeners set up');
}

function renderTabContent() {
    const tabContainer = document.getElementById('tab-container');
    if (!tabContainer) return;

    tabContainer.innerHTML = `
        ${renderMainTab()}
        ${renderActionTab()}
        ${renderConfigTab()}
        ${renderWindowTab()}
        ${renderDriverTab()}
    `;

    // Setup single character inputs after rendering
    setTimeout(() => {
        const lockKeyInput = document.getElementById('lockKey');
        if (lockKeyInput) {
            setupSingleCharInput(lockKeyInput);
        }
    }, 50);
}

function renderMainTab() {
    return `
        <!-- Main Tab -->
        <div id="main" class="tab-content active text-white">
            <div class="grid grid-cols-1 gap-4">
                <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                    <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                        <i class="fas fa-terminal mr-2"></i> Điều Khiển Bot cabal
                    </h3>
                    <div class="grid grid-cols-3 gap-3">
                        <button id="startBtn" class="control-btn bg-blue-600 hover:bg-blue-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-play text-xl mb-1"></i>
                            <span class="text-xs">Start</span>
                        </button>
                        <button id="pauseBtn" class="control-btn bg-yellow-600 hover:bg-yellow-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-pause text-xl mb-1"></i>
                            <span class="text-xs">Pause</span>
                        </button>
                        <button id="stopBtn" class="control-btn bg-red-600 hover:bg-red-700 py-2 px-3 rounded-lg flex flex-col items-center">
                            <i class="fas fa-stop text-xl mb-1"></i>
                            <span class="text-xs">Stop</span>
                        </button>
                        <div id="gameStatus" class="text-xs text-red-400 text-center col-span-3">Không tìm thấy game</div>
                    </div>
                </div>

                <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                    <h3 class="font-bold text-blue-400 mb-2 flex items-center">
                        <i class="fas fa-info-circle mr-2"></i> Hướng dẫn
                    </h3>
                    <div class="text-xs space-y-2 mt-4">
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">1.</span>
                            <span class="text-gray-300">Cài đặt driver Interception nếu chưa cài đặt.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">2.</span>
                            <span class="text-gray-300">Cấu hình hành động trong tab Hành Động.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">3.</span>
                            <span class="text-gray-300">Cấu hình phím khóa trong tab Cấu Hình.</span>
                        </div>
                        <div class="flex items-start">
                            <span class="text-yellow-400 mr-1">4.</span>
                            <span class="text-gray-300">Nhấn <span class="text-green-400">Bắt Đầu Bot</span> để bắt đầu chuỗi hành động.</span>
                        </div>
                    </div>

                    <h3 class="font-bold text-blue-400 mt-4 mb-2">Game</h3>
                    <button id="detectGameBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-1">
                        <i class="fas fa-search mr-1"></i> Tìm Game
                    </button>
                    <div id="gameDetectionStatus" class="text-xs text-gray-400 text-center">Không tìm thấy game</div>
                </div>
            </div>
        </div>
    `;
}

function renderActionTab() {
    let actionsHtml = '';
    if (appState.config && appState.config.actions) {
        for (let i = 0; i < 10; i++) {
            const action = appState.config.actions[i] || { key: '', duration: 1.0, enabled: false };
            actionsHtml += `
                <div class="flex items-center">
                    <div class="w-1/2 flex items-center">
                        <input type="checkbox" ${action.enabled ? 'checked' : ''}
                               data-action-index="${i}"
                               class="action-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                        <span class="text-sm text-gray-300">Hành động ${i + 1}</span>
                    </div>
                    <div class="w-1/2 px-3 text-sm text-gray-300">${action.key || 'Chưa cấu hình'} - ${action.duration}s</div>
                    <button data-action-index="${i}" class="edit-action-btn bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                </div>
            `;
        }
    } else {
        for (let i = 0; i < 10; i++) {
            actionsHtml += `
                <div class="flex items-center">
                    <div class="w-1/2 flex items-center">
                        <input type="checkbox" data-action-index="${i}"
                               class="action-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                        <span class="text-sm text-gray-300">Hành động ${i + 1}</span>
                    </div>
                    <div class="w-1/2 px-3 text-sm text-gray-300">Chưa cấu hình</div>
                    <button data-action-index="${i}" class="edit-action-btn bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                </div>
            `;
        }
    }

    const editingPanelHtml = appState.editingAction >= 0 ? `
        <!-- Editing Panel -->
        <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4 mt-4">
            <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                <i class="fas fa-edit mr-2"></i> Đang sửa Hành Động ${appState.editingAction + 1}
            </h3>
            <div class="grid grid-cols-1 gap-3">
                <div>
                    <label class="block text-sm text-gray-300 mb-1">Phím</label>
                    <input type="text" id="editKey" placeholder="Nhấn phím..." maxlength="1"
                           class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 text-center">
                </div>
                <div>
                    <label class="block text-sm text-gray-300 mb-1">Thời lượng (giây)</label>
                    <input type="number" id="editDuration" step="0.1" min="0.1" placeholder="1.0"
                           class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex justify-end space-x-2">
                    <button id="cancelEditBtn" class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">Hủy</button>
                    <button id="saveActionBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">Lưu</button>
                </div>
            </div>
        </div>
    ` : '';

    return `
        <!-- Action Tab -->
        <div id="action" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-list-ul mr-2"></i> Chuỗi Hành Động
                </h3>
                <div class="space-y-3 mt-4">
                    ${actionsHtml}
                </div>
            </div>
            ${editingPanelHtml}
        </div>
    `;
}

function renderConfigTab() {
    const config = appState.config || {};
    return `
        <!-- Config Tab -->
        <div id="config" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-keyboard mr-2"></i> Cấu Hình Phím
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">Phím Khóa</label>
                        <input type="text" id="lockKey" value="${config.lockKey || 'Z'}" placeholder="Z" maxlength="1"
                               class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 text-center">
                    </div>
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="text-sm text-gray-300">Khoảng Thời Gian</label>
                            <span id="intervalDisplay" class="text-xs text-blue-400">${config.timerInterval || 1.0} giây</span>
                        </div>
                        <div class="flex gap-2">
                            <input type="range" id="intervalSlider" min="0.5" max="3" step="0.1" value="${config.timerInterval || 1.0}"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm text-gray-300 mb-1">Spam nhặt đồ</label>
                        <div class="flex gap-2">
                            <div class="w-full relative flex-1">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" id="spaceSpamEnabled" ${config.spaceSpamEnabled ? 'checked' : ''}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Bật spam nhặt đồ</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="text-sm text-gray-300">Khoảng thời gian Space</label>
                            <span id="spaceIntervalDisplay" class="text-xs text-blue-400">${config.spaceSpamInterval || 200}</span>
                        </div>
                        <div class="flex gap-2">
                            <input type="range" id="spaceIntervalSlider" min="50" max="500" step="10" value="${config.spaceSpamInterval || 200}"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        </div>
                    </div>
                    <div>
                        <h3 class="font-bold text-blue-400 mb-2">Game</h3>
                        <button id="detectGameConfigBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-3">
                            <i class="fas fa-search mr-1"></i> Tìm Game
                        </button>
                        <div class="flex gap-2 mb-4">
                            <button id="saveConfigBtn" class="flex-1 bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm">
                                <i class="fas fa-save mr-1"></i> Lưu cấu hình
                            </button>
                            <button id="loadConfigBtn" class="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded text-sm">
                                <i class="fas fa-download mr-1"></i> Tải cấu hình
                            </button>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="overlayMode" ${config.overlayEnabled ? 'checked' : ''}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                            <label for="overlayMode" class="text-sm text-gray-300">Bật Chế Độ Overlay</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderWindowTab() {
    return `
        <!-- Window Tab -->
        <div id="window" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-window-maximize mr-2"></i> Quản Lý Cửa Sổ Game
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Điều Khiển Cửa Sổ Cabal</label>
                        <div class="grid grid-cols-1 gap-3">
                            <button id="setAlwaysOnTopBtn" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm">
                                <i class="fas fa-arrow-up mr-1"></i> Đặt Game Luôn Ở Trên
                            </button>
                            <button id="removeAlwaysOnTopBtn" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded text-sm">
                                <i class="fas fa-arrow-down mr-1"></i> Bỏ Luôn Ở Trên
                            </button>
                        </div>
                    </div>

                    <div id="windowStatus" class="text-xs text-gray-400"></div>

                    <div class="bg-blue-900 bg-opacity-30 rounded-lg p-3">
                        <h4 class="font-bold text-blue-300 mb-2 flex items-center">
                            <i class="fas fa-info-circle mr-2"></i> Thông Tin
                        </h4>
                        <div class="text-xs text-gray-300 space-y-2">
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">•</span>
                                <span>Khi bot chạy, game sẽ tự động được đặt luôn ở trên.</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">•</span>
                                <span>Điều này giúp bot hoạt động mà không cần click vào game.</span>
                            </div>
                            <div class="flex items-start">
                                <span class="text-yellow-400 mr-1">•</span>
                                <span>Bạn có thể tắt tính năng này bằng nút "Bỏ Luôn Ở Trên".</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderDriverTab() {
    return `
        <!-- Driver Tab -->
        <div id="driver" class="tab-content text-white" style="display: none;">
            <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                    <i class="fas fa-microchip mr-2"></i> Driver Interception
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Trạng Thái Driver Interception</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="driverStatus" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3" disabled>
                            <span class="text-sm text-gray-300">Đã cài đặt Driver</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Cài Đặt:</label>
                        <button id="installDriverBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm">
                            <i class="fas fa-download mr-1"></i> Cài Đặt Driver
                        </button>
                    </div>

                    <div id="installStatus" class="text-xs text-gray-400"></div>

                    <div class="text-xs text-yellow-400">
                        Lưu ý: Cài đặt yêu cầu quyền quản trị
                    </div>
                    <div class="text-xs text-gray-400">
                        Cần khởi động lại hệ thống
                    </div>
                </div>
            </div>
        </div>
    `;
}

function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Tab switching
    const tabBtns = document.querySelectorAll('.tab-btn');
    console.log('Found tab buttons:', tabBtns.length);
    tabBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const tabId = btn.getAttribute('data-tab');
            console.log('Tab clicked:', tabId);
            switchTab(tabId);
        });
    });

    // Use event delegation for dynamic content
    document.addEventListener('click', handleGlobalClick);
    document.addEventListener('change', handleGlobalChange);
    document.addEventListener('input', handleGlobalInput);

    // Handle window resize for responsive design
    window.addEventListener('resize', handleWindowResize);

    console.log('Event listeners set up');
}

function handleGlobalClick(e) {
    const target = e.target;

    // Bot control buttons
    if (target.id === 'startBtn') {
        e.preventDefault();
        console.log('Start button clicked');
        startBot();
    } else if (target.id === 'pauseBtn') {
        e.preventDefault();
        console.log('Pause button clicked');
        pauseBot();
    } else if (target.id === 'stopBtn') {
        e.preventDefault();
        console.log('Stop button clicked');
        stopBot();
    } else if (target.id === 'detectGameBtn' || target.id === 'detectGameConfigBtn') {
        e.preventDefault();
        console.log('Detect game button clicked');
        detectGame();
    } else if (target.id === 'installDriverBtn') {
        e.preventDefault();
        console.log('Install driver button clicked');
        installInterceptionDriver();
    } else if (target.id === 'saveConfigBtn') {
        e.preventDefault();
        console.log('Save config button clicked');
        saveConfiguration();
    } else if (target.id === 'loadConfigBtn') {
        e.preventDefault();
        console.log('Load config button clicked');
        loadConfiguration();
    } else if (target.classList.contains('edit-action-btn')) {
        e.preventDefault();
        const index = parseInt(target.getAttribute('data-action-index'));
        console.log('Edit action button clicked:', index);
        editAction(index);
    } else if (target.id === 'saveActionBtn') {
        e.preventDefault();
        console.log('Save action button clicked');
        saveAction();
    } else if (target.id === 'cancelEditBtn') {
        e.preventDefault();
        console.log('Cancel edit button clicked');
        cancelEdit();
    } else if (target.id === 'setAlwaysOnTopBtn') {
        e.preventDefault();
        console.log('Set always on top button clicked');
        setCabalAlwaysOnTop();
    } else if (target.id === 'removeAlwaysOnTopBtn') {
        e.preventDefault();
        console.log('Remove always on top button clicked');
        removeCabalAlwaysOnTop();
    }
}

function handleGlobalChange(e) {
    const target = e.target;

    if (target.classList.contains('action-checkbox')) {
        const index = parseInt(target.getAttribute('data-action-index'));
        console.log('Action checkbox changed:', index, target.checked);
        toggleAction(index, target.checked);
    } else if (target.id === 'spaceSpamEnabled') {
        console.log('Space spam checkbox changed:', target.checked);
        toggleSpaceSpam(target.checked);
    } else if (target.id === 'overlayMode') {
        console.log('Overlay checkbox changed:', target.checked);
        toggleOverlay(target.checked);
    }
}

function handleGlobalInput(e) {
    const target = e.target;

    if (target.id === 'intervalSlider') {
        console.log('Interval slider changed:', target.value);
        updateInterval(target.value);
    } else if (target.id === 'spaceIntervalSlider') {
        console.log('Space interval slider changed:', target.value);
        updateSpaceInterval(target.value);
    }
}

function switchTab(tabId) {
    // Update active tab button
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.classList.remove('active', 'text-white');
        btn.classList.add('text-gray-300');
    });

    const activeBtn = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active', 'text-white');
        activeBtn.classList.remove('text-gray-300');
    }

    // Update active tab content
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
        content.classList.remove('active');
    });

    const activeContent = document.getElementById(tabId);
    if (activeContent) {
        activeContent.style.display = 'block';
        activeContent.classList.add('active');
    }

    appState.currentTab = tabId;

    // Setup single character inputs for specific tabs
    if (tabId === 'config') {
        setTimeout(() => {
            const lockKeyInput = document.getElementById('lockKey');
            if (lockKeyInput) {
                setupSingleCharInput(lockKeyInput);
            }
        }, 50);
    }
}

function updateDateTime() {
    const now = new Date();
    const datetimeElement = document.getElementById('datetime');
    if (datetimeElement) {
        datetimeElement.textContent = now.toLocaleString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    }
}

// Bot control functions
async function startBot() {
    try {
        showNotification('Đang khởi động bot...', 'info');
        const result = await StartBot();
        console.log('Bot started:', result);
        appState.botRunning = true;
        appState.botPaused = false;
        updateBotStatus();
        showNotification('Bot đã được khởi động!', 'success');
    } catch (error) {
        console.error('Failed to start bot:', error);
        showNotification('Lỗi khi khởi động bot: ' + error.message, 'error');
    }
}

async function pauseBot() {
    try {
        showNotification('Đang tạm dừng bot...', 'info');
        // Since there's no PauseBot function, we'll use StopBot for now
        const result = await StopBot();
        console.log('Bot paused:', result);
        appState.botRunning = false;
        appState.botPaused = true;
        updateBotStatus();
        showNotification('Bot đã được tạm dừng!', 'warning');
    } catch (error) {
        console.error('Failed to pause bot:', error);
        showNotification('Lỗi khi tạm dừng bot: ' + error.message, 'error');
    }
}

async function stopBot() {
    try {
        showNotification('Đang dừng bot...', 'info');
        const result = await StopBot();
        console.log('Bot stopped:', result);
        appState.botRunning = false;
        appState.botPaused = false;
        updateBotStatus();
        showNotification('Bot đã được dừng!', 'success');
    } catch (error) {
        console.error('Failed to stop bot:', error);
        showNotification('Lỗi khi dừng bot: ' + error.message, 'error');
    }
}

async function detectGame() {
    try {
        showNotification('Đang tìm kiếm game...', 'info');
        const result = await DetectGame();
        console.log('Game detection result:', result);
        appState.gameDetected = result.found;
        updateGameStatus(result);

        if (result.found) {
            showNotification('Đã tìm thấy game Cabal!', 'success');
        } else {
            showNotification('Không tìm thấy game Cabal. Hãy đảm bảo game đang chạy.', 'warning');
        }
    } catch (error) {
        console.error('Failed to detect game:', error);
        showNotification('Lỗi khi tìm kiếm game: ' + error.message, 'error');
    }
}

function updateBotStatus() {
    // Update UI based on bot running state
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (startBtn && pauseBtn && stopBtn) {
        if (appState.botRunning) {
            // Bot is running - disable start, enable pause and stop
            startBtn.disabled = true;
            startBtn.classList.add('opacity-50');
            pauseBtn.disabled = false;
            pauseBtn.classList.remove('opacity-50');
            stopBtn.disabled = false;
            stopBtn.classList.remove('opacity-50');
        } else {
            // Bot is stopped or paused - enable start, disable pause and stop
            startBtn.disabled = false;
            startBtn.classList.remove('opacity-50');
            pauseBtn.disabled = true;
            pauseBtn.classList.add('opacity-50');
            stopBtn.disabled = true;
            stopBtn.classList.add('opacity-50');
        }
    }
}

function updateGameStatus(gameInfo) {
    const gameStatus = document.getElementById('gameStatus');
    const gameDetectionStatus = document.getElementById('gameDetectionStatus');

    if (gameInfo && gameInfo.found) {
        const statusText = 'Đã tìm thấy game';
        const statusClass = 'text-green-400';

        if (gameStatus) {
            gameStatus.textContent = statusText;
            gameStatus.className = `text-xs text-center col-span-3 ${statusClass}`;
        }
        if (gameDetectionStatus) {
            gameDetectionStatus.textContent = statusText;
            gameDetectionStatus.className = `text-xs text-center ${statusClass}`;
        }
    } else {
        const statusText = 'Không tìm thấy game';
        const statusClass = 'text-red-400';

        if (gameStatus) {
            gameStatus.textContent = statusText;
            gameStatus.className = `text-xs text-center col-span-3 ${statusClass}`;
        }
        if (gameDetectionStatus) {
            gameDetectionStatus.textContent = statusText;
            gameDetectionStatus.className = `text-xs text-center ${statusClass}`;
        }
    }
}

// Configuration functions
async function loadConfig() {
    try {
        console.log('Loading config...');
        const config = await GetConfig();
        appState.config = config;
        console.log('Config loaded:', config);

        // Re-render current tab to show updated config
        renderTabContent();
    } catch (error) {
        console.error('Failed to load config:', error);
        // Initialize with default config if loading fails
        appState.config = {
            actions: [],
            timerInterval: 1.0,
            lockKey: 'Z',
            spaceSpamEnabled: false,
            spaceSpamInterval: 200,
            overlayEnabled: false
        };
        renderTabContent();
    }
}



async function saveConfiguration() {
    try {
        const result = await SaveConfig();
        console.log('Config saved:', result);
        showNotification('Cấu hình đã được lưu!', 'success');
    } catch (error) {
        console.error('Failed to save config:', error);
        showNotification('Lỗi khi lưu cấu hình!', 'error');
    }
}

function loadConfiguration() {
    // For now, just reload the current config
    loadConfig();
}

// Action management functions
function toggleAction(index, enabled) {
    if (!appState.config) appState.config = { actions: [] };
    if (!appState.config.actions[index]) {
        appState.config.actions[index] = { key: '', duration: 1.0, enabled: false };
    }
    appState.config.actions[index].enabled = enabled;
    console.log(`Action ${index} toggled:`, enabled);
}

// Setup single character input behavior
function setupSingleCharInput(input) {
    if (!input) return;

    // Remove any existing event listeners to avoid duplicates
    input.removeEventListener('input', handleSingleCharInput);
    input.removeEventListener('keydown', handleSingleCharKeydown);

    // Add event listeners for single character behavior
    input.addEventListener('input', handleSingleCharInput);
    input.addEventListener('keydown', handleSingleCharKeydown);
}

function handleSingleCharInput(e) {
    const input = e.target;
    const value = input.value;

    // If more than 1 character, keep only the last one
    if (value.length > 1) {
        input.value = value.slice(-1);
    }
}

function handleSingleCharKeydown(e) {
    const input = e.target;

    // Allow backspace, delete, tab, escape, enter
    if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true)) {
        return;
    }

    // If there's already a character and user is typing a new one, clear the field first
    if (input.value.length >= 1 && e.key.length === 1) {
        input.value = '';
    }
}

function editAction(index) {
    console.log('Editing action:', index);
    appState.editingAction = index;

    // Set current tab to action
    appState.currentTab = 'action';

    // Re-render the tab content to show edit form
    renderTabContent();

    // Switch to action tab to make it visible
    switchTab('action');

    // Populate edit form with current values
    setTimeout(() => {
        const action = appState.config?.actions?.[index] || { key: '', duration: 1.0 };
        const keyInput = document.getElementById('editKey');
        const durationInput = document.getElementById('editDuration');

        if (keyInput) {
            keyInput.value = action.key || '';
            setupSingleCharInput(keyInput);
        }
        if (durationInput) durationInput.value = action.duration || 1.0;

        console.log('Edit form populated for action:', index);
    }, 100);
}

function cancelEdit() {
    console.log('Canceling edit');
    appState.editingAction = -1;
    renderTabContent();
    // Maintain current tab visibility after re-render
    if (appState.currentTab) {
        switchTab(appState.currentTab);
    }
}

async function saveAction() {
    console.log('Saving action...');
    const keyInput = document.getElementById('editKey');
    const durationInput = document.getElementById('editDuration');

    if (!keyInput || !durationInput) {
        console.error('Edit form inputs not found');
        return;
    }

    const key = keyInput.value.trim();
    const duration = parseFloat(durationInput.value) || 1.0;

    if (!key) {
        showNotification('Vui lòng nhập phím!', 'error');
        return;
    }

    try {
        console.log('Updating action:', appState.editingAction, 'with key:', key, 'duration:', duration);
        const result = await UpdateAction(appState.editingAction, key, duration, true);
        console.log('Action updated successfully:', result);

        // Update local state
        if (!appState.config) appState.config = {};
        if (!appState.config.actions) appState.config.actions = [];
        if (!appState.config.actions[appState.editingAction]) {
            appState.config.actions[appState.editingAction] = {};
        }
        appState.config.actions[appState.editingAction].key = key;
        appState.config.actions[appState.editingAction].duration = duration;
        appState.config.actions[appState.editingAction].enabled = true;

        appState.editingAction = -1;
        renderTabContent();
        // Maintain current tab visibility after re-render
        if (appState.currentTab) {
            switchTab(appState.currentTab);
        }

        showNotification('Hành động đã được lưu!', 'success');
    } catch (error) {
        console.error('Failed to update action:', error);
        showNotification('Lỗi khi cập nhật hành động: ' + error.message, 'error');
    }
}

// Configuration control functions
function updateInterval(value) {
    const display = document.getElementById('intervalDisplay');
    if (display) {
        display.textContent = `${value} giây`;
    }
    if (appState.config) {
        appState.config.timerInterval = parseFloat(value);
    }
}

async function updateSpaceInterval(value) {
    try {
        const display = document.getElementById('spaceIntervalDisplay');
        if (display) {
            display.textContent = value;
        }
        if (appState.config) {
            appState.config.spaceSpamInterval = parseInt(value);
        }

        // Call backend API to update space spam interval
        await SetSpaceSpamInterval(parseInt(value));
        console.log('Space spam interval updated to:', value);
    } catch (error) {
        console.error('Failed to update space spam interval:', error);
        showNotification('Lỗi khi cập nhật khoảng thời gian space: ' + error.message, 'error');
    }
}

async function toggleSpaceSpam(enabled) {
    try {
        console.log('Space spam toggled:', enabled);

        // Update local state
        if (appState.config) {
            appState.config.spaceSpamEnabled = enabled;
        }

        // Call backend API to actually enable/disable space spam
        await ToggleSpaceSpam(enabled);

        // Show notification
        if (enabled) {
            showNotification('Spam nhặt đồ đã được bật', 'success');
        } else {
            showNotification('Spam nhặt đồ đã được tắt', 'info');
        }
    } catch (error) {
        console.error('Failed to toggle space spam:', error);
        showNotification('Lỗi khi thay đổi spam nhặt đồ: ' + error.message, 'error');

        // Revert checkbox state on error
        const checkbox = document.getElementById('spaceSpamEnabled');
        if (checkbox) {
            checkbox.checked = !enabled;
        }
        if (appState.config) {
            appState.config.spaceSpamEnabled = !enabled;
        }
    }
}

function toggleOverlay(enabled) {
    if (appState.config) {
        appState.config.overlayEnabled = enabled;
    }
    console.log('Overlay toggled:', enabled);
}

function recordLockKey() {
    showNotification('Nhấn phím bạn muốn sử dụng làm phím khóa...', 'info');
    // Simple implementation - just show notification for now
    // The actual key recording will be implemented later without crashes
}

// Window management functions
async function setCabalAlwaysOnTop() {
    try {
        const statusDiv = document.getElementById('windowStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Đang đặt game luôn ở trên...';
            statusDiv.className = 'text-xs text-yellow-400';
        }

        const result = await SetCabalAlwaysOnTop();
        console.log('Set always on top result:', result);

        if (statusDiv) {
            statusDiv.textContent = 'Game đã được đặt luôn ở trên!';
            statusDiv.className = 'text-xs text-green-400';
        }

        showNotification('Game đã được đặt luôn ở trên', 'success');
    } catch (error) {
        console.error('Failed to set always on top:', error);
        const statusDiv = document.getElementById('windowStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Lỗi: Không thể đặt game luôn ở trên';
            statusDiv.className = 'text-xs text-red-400';
        }
        showNotification('Lỗi khi đặt game luôn ở trên: ' + error.message, 'error');
    }
}

async function removeCabalAlwaysOnTop() {
    try {
        const statusDiv = document.getElementById('windowStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Đang bỏ tính năng luôn ở trên...';
            statusDiv.className = 'text-xs text-yellow-400';
        }

        const result = await RemoveCabalAlwaysOnTop();
        console.log('Remove always on top result:', result);

        if (statusDiv) {
            statusDiv.textContent = 'Đã bỏ tính năng luôn ở trên!';
            statusDiv.className = 'text-xs text-green-400';
        }

        showNotification('Đã bỏ tính năng luôn ở trên', 'success');
    } catch (error) {
        console.error('Failed to remove always on top:', error);
        const statusDiv = document.getElementById('windowStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Lỗi: Không thể bỏ tính năng luôn ở trên';
            statusDiv.className = 'text-xs text-red-400';
        }
        showNotification('Lỗi khi bỏ tính năng luôn ở trên: ' + error.message, 'error');
    }
}

// Driver functions
async function installInterceptionDriver() {
    try {
        const statusDiv = document.getElementById('installStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Đang cài đặt driver...';
            statusDiv.className = 'text-xs text-yellow-400';
        }

        const result = await InstallDriver();
        console.log('Driver installation result:', result);

        if (statusDiv) {
            statusDiv.textContent = 'Cài đặt thành công! Cần khởi động lại hệ thống.';
            statusDiv.className = 'text-xs text-green-400';
        }
    } catch (error) {
        console.error('Failed to install driver:', error);
        const statusDiv = document.getElementById('installStatus');
        if (statusDiv) {
            statusDiv.textContent = 'Cài đặt thất bại! Vui lòng chạy với quyền quản trị.';
            statusDiv.className = 'text-xs text-red-400';
        }
    }
}

// Make functions globally available
window.startBot = startBot;
window.pauseBot = pauseBot;
window.stopBot = stopBot;
window.toggleAction = toggleAction;
window.editAction = editAction;
window.cancelEdit = cancelEdit;
window.saveAction = saveAction;
window.updateInterval = updateInterval;
window.updateSpaceInterval = updateSpaceInterval;
window.toggleSpaceSpam = toggleSpaceSpam;
window.toggleOverlay = toggleOverlay;
window.recordLockKey = recordLockKey;
window.detectGame = detectGame;
window.saveConfiguration = saveConfiguration;
window.loadConfiguration = loadConfiguration;
window.installInterceptionDriver = installInterceptionDriver;
window.setCabalAlwaysOnTop = setCabalAlwaysOnTop;
window.removeCabalAlwaysOnTop = removeCabalAlwaysOnTop;

// Removed unused functions - using event delegation instead

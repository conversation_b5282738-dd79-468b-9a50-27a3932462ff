package main

import (
	"context"
	"log"
	"os"
)

// App struct - Wails application context
type App struct {
	ctx context.Context
}

// Global state - reuse existing AppState
var state AppState

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	// Initialize logger to file instead of console
	initLogger()

	// Create default config - reuse existing initialization
	state.config = NewBotConfig()
	state.statusMessage = translate("Ready")
	state.editingAction = -1
	state.tempKey = ""
	state.tempDuration = ""
	state.transparency = 0.8
	state.overlayPosition = "top-right"
	state.directInputEnabled = true
	state.overlayEnabled = false

	// Initialize wizard state
	state.wizardState = WizardState{
		CurrentStep: 1,
		TotalSteps:  4,
		Completed:   false,
	}

	// Initialize key recording state
	state.keyRecordingState = KeyRecordingState{
		Recording:      false,
		TargetKey:      nil,
		TargetScanCode: nil,
	}

	// Initialize game process info
	state.gameProcess = GameProcessInfo{
		ProcessID: 0,
		Path:      "",
		Found:     false,
	}

	// Check if driver is installed
	if IsInterceptionDriverInstalled() {
		state.driverStatus = translate("✓ Driver installed")
	} else {
		state.driverStatus = translate("⚠ Driver not installed")
	}

	// Initialize keyboard hook
	initKeyboardHook()
}

// Initialize logger to file instead of console - reuse existing function
func initLogger() {
	logFile, err := os.OpenFile("cabal_bot.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return
	}
	state.logFile = logFile
	log.SetOutput(logFile)
	os.Stdout = logFile
	os.Stderr = logFile
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	log.Println("Logger initialized")
}

// processKeyboardInput processes keyboard input captured by the global hook
func processKeyboardInput(key string, scanCode uint16) {
	if state.keyRecordingState.Recording {
		// Check if we need to update a target key
		if state.keyRecordingState.TargetKey != nil {
			*state.keyRecordingState.TargetKey = key

			// If we also need to update the scan code
			if state.keyRecordingState.TargetScanCode != nil {
				*state.keyRecordingState.TargetScanCode = scanCode
			}

			// Stop recording
			stopRecordingKey()
		}
	}
}

// =============================================================================
// API METHODS FOR FRONTEND - These replace the old UI functions
// =============================================================================

// GetAppState returns the current application state for the frontend
func (a *App) GetAppState() map[string]interface{} {
	return map[string]interface{}{
		"config":            state.config,
		"statusMessage":     state.statusMessage,
		"driverStatus":      state.driverStatus,
		"installStatus":     state.installStatus,
		"gameProcess":       state.gameProcess,
		"wizardState":       state.wizardState,
		"keyRecordingState": state.keyRecordingState,
		"overlayEnabled":    state.overlayEnabled,
		"editingAction":     state.editingAction,
		"tempKey":           state.tempKey,
		"tempDuration":      state.tempDuration,
	}
}

// StartBot starts the bot - reuse existing function
func (a *App) StartBot() string {
	startBot()
	return translate("Bot started")
}

// StopBot stops the bot - reuse existing function
func (a *App) StopBot() string {
	stopBot()
	return translate("Bot stopped")
}

// DetectGame detects the game process - reuse existing function
func (a *App) DetectGame() map[string]interface{} {
	detectGameProcess()
	return map[string]interface{}{
		"found": state.gameProcess.Found,
		"path":  state.gameProcess.Path,
	}
}

// InstallDriver installs the interception driver - reuse existing function
func (a *App) InstallDriver() string {
	installDriver()
	return state.installStatus
}

// SaveConfig saves the configuration - reuse existing function
func (a *App) SaveConfig() string {
	saveConfigToFile()
	return translate("Configuration saved")
}

// LoadConfig loads the configuration - reuse existing function
func (a *App) LoadConfig() string {
	loadConfigFromFile()
	return translate("Configuration loaded")
}

// GetConfig returns the current configuration
func (a *App) GetConfig() map[string]interface{} {
	if state.config == nil {
		return map[string]interface{}{}
	}

	// Convert actions to a serializable format
	actions := make([]map[string]interface{}, len(state.config.Actions))
	for i, action := range state.config.Actions {
		actions[i] = map[string]interface{}{
			"key":      action.Key,
			"scanCode": action.ScanCode,
			"duration": action.Duration,
			"enabled":  action.Enabled,
		}
	}

	return map[string]interface{}{
		"actions":            actions,
		"timerInterval":      state.config.TimerInterval,
		"lockKey":            state.config.LockKey,
		"lockScanCode":       state.config.LockScanCode,
		"spaceSpamEnabled":   state.config.SpaceSpamEnabled,
		"spaceSpamInterval":  state.config.SpaceSpamInterval,
		"running":            state.config.Running,
		"overlayEnabled":     state.overlayEnabled,
		"overlayPosition":    state.overlayPosition,
		"transparency":       state.transparency,
		"directInputEnabled": state.directInputEnabled,
	}
}

// UpdateAction updates an action in the configuration
func (a *App) UpdateAction(index int, key string, duration float64, enabled bool) string {
	if index >= 0 && index < len(state.config.Actions) {
		state.config.Actions[index].Key = key
		state.config.Actions[index].Duration = duration
		state.config.Actions[index].Enabled = enabled

		// Update scan code if key is valid
		if scanCode, ok := keyNameToScanCode[key]; ok {
			state.config.Actions[index].ScanCode = scanCode
		}

		return translate("Action updated")
	}
	return translate("Invalid action index")
}

// SetLockKey sets the lock key for the bot
func (a *App) SetLockKey(key string) string {
	if scanCode, ok := keyNameToScanCode[key]; ok {
		state.config.LockKey = key
		state.config.LockScanCode = scanCode
		return translate("Lock key set")
	}
	return translate("Invalid key")
}

// SetTimerInterval sets the timer interval
func (a *App) SetTimerInterval(interval float64) string {
	if interval >= 0.1 && interval <= 10.0 {
		state.config.TimerInterval = interval
		return translate("Timer interval set")
	}
	return translate("Invalid interval")
}

// SetSpaceSpamInterval sets the space spam interval
func (a *App) SetSpaceSpamInterval(interval int) string {
	if interval >= 50 && interval <= 2000 {
		state.config.SpaceSpamInterval = interval
		return translate("Space spam interval set")
	}
	return translate("Invalid interval")
}

// ToggleSpaceSpam toggles space spam functionality
func (a *App) ToggleSpaceSpam(enabled bool) string {
	state.config.SpaceSpamEnabled = enabled
	return translate("Space spam toggled")
}

// ToggleOverlay toggles overlay mode
func (a *App) ToggleOverlay(enabled bool) string {
	state.overlayEnabled = enabled
	return translate("Overlay toggled")
}

// StartKeyRecording starts recording a key press
func (a *App) StartKeyRecording() string {
	state.keyRecordingState.Recording = true
	return translate("Recording key - press any key")
}

// StopKeyRecording stops recording a key press
func (a *App) StopKeyRecording() string {
	state.keyRecordingState.Recording = false
	state.keyRecordingState.TargetKey = nil
	state.keyRecordingState.TargetScanCode = nil
	return translate("Key recording stopped")
}

// GetAvailableKeys returns list of available keys
func (a *App) GetAvailableKeys() []string {
	return getAllKeyNames()
}

// SetCabalAlwaysOnTop sets the Cabal game window to always stay on top
func (a *App) SetCabalAlwaysOnTop() string {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Panic in SetCabalAlwaysOnTop: %v", r)
		}
	}()

	err := SetCabalWindowAlwaysOnTop()
	if err != nil {
		log.Printf("Failed to set Cabal window always on top: %v", err)
		return translate("Failed to set game window always on top: ") + err.Error()
	}
	return translate("Game window set to always on top")
}

// RemoveCabalAlwaysOnTop removes the always on top setting from the Cabal game window
func (a *App) RemoveCabalAlwaysOnTop() string {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Panic in RemoveCabalAlwaysOnTop: %v", r)
		}
	}()

	err := RemoveCabalWindowAlwaysOnTop()
	if err != nil {
		log.Printf("Failed to remove Cabal window always on top: %v", err)
		return translate("Failed to remove always on top: ") + err.Error()
	}
	return translate("Always on top setting removed")
}

// =============================================================================
// WIZARD API METHODS - Reuse existing wizard logic
// =============================================================================

// GetWizardState returns the current wizard state
func (a *App) GetWizardState() map[string]interface{} {
	return map[string]interface{}{
		"currentStep": state.wizardState.CurrentStep,
		"totalSteps":  state.wizardState.TotalSteps,
		"completed":   state.wizardState.Completed,
	}
}

// NextWizardStep advances to the next wizard step
func (a *App) NextWizardStep() map[string]interface{} {
	if state.wizardState.CurrentStep < state.wizardState.TotalSteps {
		state.wizardState.CurrentStep++
	} else {
		state.wizardState.Completed = true
	}
	return a.GetWizardState()
}

// PreviousWizardStep goes back to the previous wizard step
func (a *App) PreviousWizardStep() map[string]interface{} {
	if state.wizardState.CurrentStep > 1 {
		state.wizardState.CurrentStep--
	}
	return a.GetWizardState()
}

// CompleteWizard marks the wizard as completed
func (a *App) CompleteWizard() string {
	state.wizardState.Completed = true
	return translate("Wizard completed")
}

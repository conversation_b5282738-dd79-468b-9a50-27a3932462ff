package main

// startRec<PERSON><PERSON><PERSON><PERSON> starts recording a key press
func startRecording<PERSON>ey(targetKey *string, targetScanCode *uint16) {
	state.keyRecordingState.Recording = true
	state.keyRecordingState.TargetKey = targetKey
	state.keyRecordingState.TargetScanCode = targetScanCode
	state.statusMessage = translate("Recording key")
}

// stopRecording<PERSON><PERSON> stops recording a key press
func stopRecordingKey() {
	state.keyRecordingState.Recording = false
	state.keyRecordingState.TargetKey = nil
	state.keyRecordingState.TargetScanCode = nil
}

// moveToNextWizardStep advances to the next wizard step
func moveToNextWizardStep() {
	if state.wizardState.CurrentStep < state.wizardState.TotalSteps {
		state.wizardState.CurrentStep++
	} else {
		state.wizardState.Completed = true
	}
}

// moveToPreviousWizardStep goes back to the previous wizard step
func moveToPreviousWizardStep() {
	if state.wizardState.CurrentStep > 1 {
		state.wizardState.CurrentStep--
	}
}

// Note: Wizard UI rendering is now handled by the web frontend
// The wizard state management functions are available via the API

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Bot Control Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            position: relative;
        }
        
        .app-container {
            width: 600px;
            height: 400px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: -1;
        }
        
        .tab-btn.active {
            border-bottom: 3px solid #3B82F6;
            color: white;
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .control-btn {
            transition: all 0.2s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
        }
        
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-900 p-4">
    <div class="app-container">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0" style="background-image: url('https://i.ibb.co/67P9MrGT/auto-bgr.png'); background-size: cover; background-position: center;"></div>
        <div class="overlay"></div>
        
        <!-- Main Content -->
        <div class="relative z-10 h-full flex flex-col">
            <!-- Header with Logo -->
            <div class="flex items-center justify-between px-4 py-2 bg-black bg-opacity-30">
                <div class="flex items-center">
                    <h1 class="text-white font-bold text-xl">CABAL<strong class="text-blue-400">BOT</strong> v2.0</h1>
                </div>
                <div class="text-xs text-gray-300">
                    <span id="datetime"></span>
                </div>
            </div>
            
            <!-- Tab Navigation -->
            <div class="flex border-b border-gray-700 bg-black bg-opacity-40">
                <button class="tab-btn active flex-1 py-3 px-4 text-white font-medium text-sm tracking-wider" data-tab="main">
                    <i class="fas fa-home mr-2"></i> Main
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="action">
                    <i class="fas fa-play-circle mr-2"></i> Action
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="config">
                    <i class="fas fa-cog mr-2"></i> Config
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-gray-300 font-medium text-sm tracking-wider" data-tab="driver">
                    <i class="fas fa-microchip mr-2"></i> Driver
                </button>
            </div>
            
            <!-- Tab Contents -->
            <div class="flex-1 overflow-y-auto p-4 bg-black bg-opacity-30">
                <!-- Main Tab -->
                <div id="main" class="tab-content active text-white">
                    <div class="grid grid-cols-1 gap-4">
                        <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                            <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                                <i class="fas fa-terminal mr-2"></i> Điều Khiển Bot cabal
                            </h3>
                            <div class="grid grid-cols-3 gap-3">
                                <button class="control-btn bg-blue-600 hover:bg-blue-700 py-2 px-3 rounded-lg flex flex-col items-center">
                                    <i class="fas fa-play text-xl mb-1"></i>
                                    <span class="text-xs">Start</span>
                                </button>
                                <button class="control-btn bg-yellow-600 hover:bg-yellow-700 py-2 px-3 rounded-lg flex flex-col items-center">
                                    <i class="fas fa-pause text-xl mb-1"></i>
                                    <span class="text-xs">Pause</span>
                                </button>
                                <button class="control-btn bg-red-600 hover:bg-red-700 py-2 px-3 rounded-lg flex flex-col items-center">
                                    <i class="fas fa-stop text-xl mb-1"></i>
                                    <span class="text-xs">Stop</span>
                                </button>
                                <div class="text-xs text-green-400 text-center col-span-3">Đã tìm thấy game</div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                            <h3 class="font-bold text-blue-400 mb-2 flex items-center">
                                <i class="fas fa-info-circle mr-2"></i> Hướng dẫn
                            </h3>
                            <div class="text-xs space-y-2">
                            </div>

                            <div class="text-xs space-y-2 mt-4">
                                <div class="flex items-start">
                                    <span class="text-yellow-400 mr-1">1.</span>
                                    <span class="text-gray-300">Cài đặt driver Interception nếu chưa cài đặt.</span>
                                </div>
                                <div class="flex items-start">
                                    <span class="text-yellow-400 mr-1">2.</span>
                                    <span class="text-gray-300">Cấu hình hành động trong tab Hành Động.</span>
                                </div>
                                <div class="flex items-start">
                                    <span class="text-yellow-400 mr-1">3.</span>
                                    <span class="text-gray-300">Cấu hình phím khóa trong tab Cấu Hình.</span>
                                </div>
                                <div class="flex items-start">
                                    <span class="text-yellow-400 mr-1">4.</span>
                                    <span class="text-gray-300">Nhấn <span class="text-green-400">Bắt Đầu Bot</span> để bắt đầu chuỗi hành động.</span>
                                </div>
                            </div>

                            <h3 class="font-bold text-blue-400 mt-4 mb-2">Game</h3>
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-1">
                                <i class="fas fa-search mr-1"></i> Tìm Game
                            </button>
                            <div class="text-xs text-gray-400 text-center">Không tìm thấy game</div>
                        </div>

                
                    </div>
                </div>
                
                <!-- Action Tab -->
                <div id="action" class="tab-content text-white">
                    <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                        <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                            <i class="fas fa-list-ul mr-2"></i> Chuỗi Hành Động
                        </h3>
                        <div class="space-y-3 mt-4">
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 1</span>
                                </div>
                                <div class="w-1/2 relative">
                                    <select class="appearance-none w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500">
                                        <option>Nội dung hành động</option>
                                        <option>Tùy chọn 1</option>
                                        <option>Tùy chọn 2</option>
                                        <option>Tùy chọn 3</option>
                                    </select>
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-300">
                                        <i class="fas fa-chevron-down text-xs"></i>
                                    </div>
                                </div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 2</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 3</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 4</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 5</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 6</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 7</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 8</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 9</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                            <div class="flex items-center">
                                <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Hành động 10</span>
                                </div>
                                <div class="w-1/2 px-3 text-sm text-gray-300">Nội dung hành động</div>
                                <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-xs">Sửa</button>
                            </div>
                        </div>
                    </div>
                       <!-- Editing Panel -->
                        <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4 mt-4">
                            <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                                <i class="fas fa-edit mr-2"></i> Đang sửa Hành Động 1
                            </h3>
                            <div class="grid grid-cols-1 gap-3">
                                <div>
                                    <label class="block text-sm text-gray-300 mb-1">Phím</label>
                                    <div class="w-full relative">
                                        <select class="appearance-none w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option>Hành Động 1</option>
                                            <option>Hành Động 2</option>
                                            <option>Hành Động 3</option>
                                        </select>
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-300">
                                            <i class="fas fa-chevron-down text-xs"></i>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-300 mb-1">Thời lượng</label>
                                    <input type="text" placeholder="Nội dung hành động" class="w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="flex justify-end space-x-2">
                                    <button class="bg-gray-700 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">Hủy</button>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">Lưu</button>
                                </div>
                            </div>
                        </div>
                </div>
                
                <!-- Config Tab -->
                <div id="config" class="tab-content text-white">
                    <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                        <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                            <i class="fas fa-keyboard mr-2"></i> Cấu Hình Phím
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm text-gray-300 mb-1">Phím Khóa</label>
                                <div class="flex gap-2">
                                    <div class="w-full relative flex-1">
                                        <select class="appearance-none w-full bg-gray-900 bg-opacity-70 border border-gray-700 rounded px-3 py-1 text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500">
                                            <option>Chọn phím...</option>
                                            <option>Space</option>
                                            <option>F1 - F12</option>
                                            <option>0 - 9</option>
                                            <option>A - Z</option>
                                        </select>
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-300">
                                            <i class="fas fa-chevron-down text-xs"></i>
                                        </div>
                                    </div>
                                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">Nhấn phím cho hành động</button>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <label class="text-sm text-gray-300">Khoảng Thời Gian</label>
                                    <span class="text-xs text-blue-400">1.0 giây</span>
                                </div>
                                <div class="flex gap-2">
                                    <div class="w-full relative flex-1">
                                        <input type="range" min="0.5" max="3" step="0.1" value="1.0" 
                                               class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm text-gray-300 mb-1">Spam nhặt đồ</label>
                                <div class="flex gap-2">
                                    <div class="w-full relative flex-1">
                                         <div class="w-1/2 flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <span class="text-sm text-gray-300">Bật spam nhặt đồ</span>
                                </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <label class="text-sm text-gray-300">Khoảng thời gian Space</label>
                                    <span class="text-xs text-blue-400">200</span>
                                </div>
                                <div class="flex gap-2">
                                    <div class="w-full relative flex-1">
                                        <input type="range" min="1" max="200" step="1" value="100" 
                                               class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h3 class="font-bold text-blue-400 mb-2">Game</h3>
                                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm mb-3">
                                    <i class="fas fa-search mr-1"></i> Tìm Game
                                </button>
                                <div class="flex gap-2 mb-4">
                                    <button class="flex-1 bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm">
                                        <i class="fas fa-save mr-1"></i> Lưu cấu hình
                                    </button>
                                    <button class="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded text-sm">
                                        <i class="fas fa-download mr-1"></i> Tải cấu hình
                                    </button>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="overlayMode" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3">
                                    <label for="overlayMode" class="text-sm text-gray-300">Bật Chế Độ Overlay</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Driver Tab -->
                <div id="driver" class="tab-content text-white">
                    <div class="bg-gray-800 bg-opacity-60 rounded-lg p-4">
                        <h3 class="font-bold text-blue-400 mb-3 flex items-center">
                            <i class="fas fa-microchip mr-2"></i> Driver Interception
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Trạng Thái Driver Interception</label>
                                <div class="flex items-center">
                                    <input type="checkbox" id="driverStatus" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded bg-gray-900 bg-opacity-70 mr-3" disabled>
                                    <span class="text-sm text-gray-300">Đã cài đặt Driver</span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Cài Đặt:</label>
                                <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm">
                                    <i class="fas fa-download mr-1"></i> Cài Đặt Driver
                                </button>
                            </div>
                            
                            <div class="text-xs text-yellow-400">
                                Lưu ý: Cài đặt yêu cầu quyền quản trị
                            </div>
                            <div class="text-xs text-gray-400">
                                Cần khởi động lại hệ thống
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Status Bar -->
            <div class="bg-black bg-opacity-60 text-xs text-gray-400 px-4 py-2 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                    <span>System Ready</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span><i class="fas fa-satellite-dish mr-1"></i> 87%</span>
                    <span><i class="fas fa-database mr-1"></i> 24%</span>
                    <span><i class="fas fa-plug mr-1"></i> 3/4</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab Switching
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.getAttribute('data-tab');
                
                // Update active tab button
                tabBtns.forEach(b => b.classList.remove('active', 'text-white'));
                tabBtns.forEach(b => b.classList.add('text-gray-300'));
                btn.classList.add('active', 'text-white');
                btn.classList.remove('text-gray-300');
                
                // Update active tab content
                tabContents.forEach(content => content.classList.remove('active'));
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Update datetime
        function updateDateTime() {
            const now = new Date();
            const datetimeElement = document.getElementById('datetime');
            datetimeElement.textContent = now.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }
        
        updateDateTime();
        setInterval(updateDateTime, 60000);
    </script>
</body>
</html>